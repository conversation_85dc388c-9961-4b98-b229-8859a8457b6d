# 📱 تعليمات تشغيل تطبيق الفواتير النهائية

## 🎯 ملخص المشروع

تم إنشاء تطبيق فواتير شامل ومتكامل باستخدام Flutter مع العمارة النظيفة (Clean Architecture). التطبيق يحتوي على جميع الميزات المطلوبة لإنشاء وإدارة الفواتير بطريقة احترافية.

## 🚀 طرق التشغيل (مرتبة حسب السهولة)

### الطريقة الأولى: التشغيل المباشر
1. انقر مرتين على ملف `START_APP.bat`
2. انتظر حتى يكتمل التحميل
3. تأكد من وجود جهاز Android متصل أو محاكي يعمل

### الطريقة الثانية: PowerShell
```powershell
# افتح PowerShell في مجلد المشروع وشغل:
.\quick_run.ps1
```

### الطريقة الثالثة: الأوامر اليدوية
```bash
cd d:\invoice
flutter pub get
flutter run
```

## 🔧 حل مشاكل الترميز

إذا واجهت مشاكل في الترميز:

### الحل الأول:
```cmd
# شغل هذا الملف:
fix_encoding.ps1
```

### الحل الثاني:
```cmd
# في Command Prompt:
chcp 65001
cd d:\invoice
flutter clean
flutter pub get
flutter run
```

## 📱 متطلبات التشغيل

### المتطلبات الأساسية:
- ✅ Flutter SDK (مثبت ومضاف إلى PATH)
- ✅ Android Studio أو VS Code
- ✅ جهاز Android أو محاكي Android يعمل

### التحقق من المتطلبات:
```bash
# تحقق من Flutter
flutter doctor

# تحقق من الأجهزة المتصلة
flutter devices
```

## 🎨 ميزات التطبيق المكتملة

### 📋 إدارة الفواتير
- ✅ إنشاء فواتير جديدة مع سير عمل كامل
- ✅ قائمة الفواتير مع إحصائيات
- ✅ معاينة احترافية للفواتير
- ✅ تصفية وبحث في الفواتير

### 💼 إدارة البيانات
- ✅ إدارة معلومات الأعمال (اسم، عنوان، هاتف، إيميل)
- ✅ إدارة معلومات العملاء
- ✅ إضافة عناصر متعددة (منتجات/خدمات)
- ✅ حسابات تلقائية للمجاميع

### 🧮 الحسابات المتقدمة
- ✅ حساب المجموع الفرعي تلقائياً
- ✅ إضافة ضرائب (نسبة مئوية أو مبلغ ثابت)
- ✅ إضافة خصومات (نسبة مئوية أو مبلغ ثابت)
- ✅ إضافة تكاليف الشحن
- ✅ حساب الإجمالي النهائي

### 🎨 التخصيص
- ✅ **6 قوالب احترافية**: Modern, Classic, Minimal, Creative, Corporate, Service
- ✅ **10+ لغات**: العربية، الإنجليزية، الفرنسية، الإسبانية، الألمانية، وغيرها
- ✅ **20+ عملة**: الريال السعودي، الدولار، اليورو، الجنيه، وغيرها
- ✅ **توقيعات رقمية**: إضافة توقيع نصي
- ✅ **شروط وأحكام**: قوالب جاهزة أو نص مخصص
- ✅ **8 طرق دفع**: تحويل بنكي، بطاقة ائتمان، PayPal، نقداً، وغيرها

## 📱 كيفية استخدام التطبيق

### عند فتح التطبيق لأول مرة:
1. ستظهر الشاشة الرئيسية مع قائمة فواتير فارغة
2. اضغط على زر (+) الأزرق لإنشاء فاتورة جديدة

### إنشاء فاتورة جديدة:
1. **معلومات الأعمال**: اضغط "Business Info" وأدخل بيانات شركتك
2. **معلومات العميل**: اضغط "Client Info" وأدخل بيانات العميل
3. **إضافة عناصر**: اضغط "Add Item" وأضف المنتجات/الخدمات
4. **اختيار اللغة**: اضغط "English Invoice Language" واختر اللغة
5. **اختيار القالب**: اضغط "Templates" واختر التصميم المفضل
6. **اختيار العملة**: اضغط "SAR ج Currency" واختر العملة
7. **إضافة حسابات**: اضغط على "Discount", "Tax", "Shipping" لإضافة قيم
8. **التوقيع**: اضغط "Signature" وأضف توقيعك
9. **الشروط**: اضغط "Terms & Conditions" وأضف الشروط
10. **طريقة الدفع**: اضغط "Payment Method" واختر الطريقة
11. **المعاينة**: اضغط "Preview" لرؤية الفاتورة النهائية
12. **الحفظ**: اضغط "Save" لحفظ الفاتورة

## 🏗️ هيكل المشروع

```
d:\invoice\
├── lib\
│   ├── features\invoice\presentation\screens\
│   │   ├── invoice_list_screen.dart          # الشاشة الرئيسية
│   │   ├── create_invoice_screen.dart        # إنشاء فاتورة
│   │   ├── business_info_screen.dart         # معلومات الأعمال
│   │   ├── client_info_screen.dart           # معلومات العميل
│   │   ├── add_item_screen.dart              # إضافة عنصر
│   │   ├── invoice_preview_screen.dart       # معاينة الفاتورة
│   │   ├── language_selection_screen.dart    # اختيار اللغة
│   │   ├── template_selection_screen.dart    # اختيار القالب
│   │   ├── currency_selection_screen.dart    # اختيار العملة
│   │   ├── signature_screen.dart             # التوقيع
│   │   ├── terms_conditions_screen.dart      # الشروط والأحكام
│   │   ├── payment_method_screen.dart        # طريقة الدفع
│   │   └── calculation_dialog.dart           # حوار الحسابات
│   └── main.dart                             # الملف الرئيسي
├── START_APP.bat                             # تشغيل سريع
├── quick_run.ps1                             # تشغيل PowerShell
├── fix_encoding.ps1                          # حل مشاكل الترميز
└── QUICK_START.md                            # دليل التشغيل السريع
```

## 🔍 استكشاف الأخطاء

### المشكلة: "Flutter not found"
**الحل**: تأكد من تثبيت Flutter وإضافته إلى PATH

### المشكلة: "No devices found"
**الحل**: 
- تأكد من تشغيل محاكي Android
- أو ربط جهاز Android مع تفعيل USB Debugging

### المشكلة: مشاكل في الترميز
**الحل**: شغل `fix_encoding.ps1`

### المشكلة: فشل في تحميل التبعيات
**الحل**:
```bash
flutter clean
flutter pub get
```

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. تأكد من تحديث Flutter: `flutter upgrade`
2. نظف المشروع: `flutter clean`
3. أعد تثبيت التبعيات: `flutter pub get`
4. تحقق من حالة النظام: `flutter doctor`

## 🎉 تهانينا!

لديك الآن تطبيق فواتير متكامل وجاهز للاستخدام مع جميع الميزات الاحترافية! 

**استمتع بإنشاء فواتير احترافية! 🚀**
