# كيفية تشغيل تطبيق الفواتير

## المتطلبات الأساسية

1. **Flutter SDK**: تأكد من تثبيت Flutter على جهازك
2. **Android Studio**: لتشغيل المحاكي أو ربط جهاز Android
3. **جهاز Android** أو **محاكي Android**: لتشغيل التطبيق

## طرق التشغيل

### الطريقة الأولى: التشغيل السريع
```powershell
# افتح PowerShell كمدير وشغل:
.\quick_run.ps1
```

### الطريقة الثانية: التشغيل الكامل مع التحقق
```powershell
# افتح PowerShell كمدير وشغل:
.\run_app.ps1
```

### الطريقة الثالثة: التشغيل اليدوي
```bash
# 1. تثبيت التبعيات
flutter pub get

# 2. توليد الكود (اختياري)
flutter packages pub run build_runner build

# 3. تشغيل التطبيق
flutter run
```

## حل المشاكل الشائعة

### مشكلة الترميز
إذا واجهت مشاكل في الترميز، استخدم:
```powershell
chcp 65001
```

### مشكلة عدم العثور على Flutter
تأكد من إضافة Flutter إلى PATH:
```powershell
# تحقق من تثبيت Flutter
flutter doctor
```

### مشكلة عدم وجود جهاز
```bash
# تحقق من الأجهزة المتصلة
flutter devices

# تشغيل محاكي Android
flutter emulators --launch <emulator_name>
```

## خطوات التشغيل التفصيلية

### 1. تحضير البيئة
- تأكد من تثبيت Flutter SDK
- تأكد من تثبيت Android Studio
- قم بتشغيل محاكي Android أو ربط جهاز Android

### 2. تحضير المشروع
```bash
cd d:\invoice
flutter clean
flutter pub get
```

### 3. تشغيل التطبيق
```bash
flutter run
```

## ميزات التطبيق المتاحة

عند تشغيل التطبيق ستجد:

### الشاشة الرئيسية
- قائمة الفواتير
- إحصائيات المبالغ المستحقة
- فلاتر للبحث والتصفية
- زر إضافة فاتورة جديدة (+)

### إنشاء فاتورة جديدة
1. **معلومات الأعمال**: أضف تفاصيل شركتك
2. **معلومات العميل**: أضف بيانات العميل
3. **العناصر**: أضف المنتجات/الخدمات
4. **الحسابات**: أضف الضرائب والخصومات
5. **الإعدادات**: اختر اللغة والقالب والعملة
6. **التوقيع**: أضف توقيعك الرقمي
7. **الشروط**: أضف الشروط والأحكام
8. **طريقة الدفع**: اختر طريقة الدفع

### المعاينة والحفظ
- معاينة الفاتورة بتصميم احترافي
- حفظ الفاتورة في قاعدة البيانات المحلية
- إمكانية التعديل والتحديث

## الدعم الفني

إذا واجهت أي مشاكل:

1. تأكد من تحديث Flutter: `flutter upgrade`
2. نظف المشروع: `flutter clean`
3. أعد تثبيت التبعيات: `flutter pub get`
4. تحقق من حالة Flutter: `flutter doctor`

## معلومات إضافية

- **اللغات المدعومة**: 10+ لغات
- **العملات المدعومة**: 20+ عملة
- **القوالب المتاحة**: 6 قوالب احترافية
- **طرق الدفع**: 8 طرق دفع مختلفة

---

**ملاحظة**: تأكد من وجود اتصال بالإنترنت عند التشغيل الأول لتحميل التبعيات.
