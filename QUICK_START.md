# 🚀 تشغيل سريع لتطبيق الفواتير

## خطوات التشغيل السريع

### 1. تحضير الجهاز
- تأكد من تشغيل محاكي Android أو ربط جهاز Android
- تأكد من تثبيت Flutter على جهازك

### 2. تشغيل التطبيق
اختر إحدى الطرق التالية:

#### الطريقة الأسهل (Windows):
```
انقر مرتين على ملف: START_APP.bat
```

#### أو استخدم PowerShell:
```powershell
.\quick_run.ps1
```

#### أو استخدم الأوامر اليدوية:
```bash
flutter pub get
flutter run
```

## حل المشاكل السريع

### إذا واجهت مشاكل في الترميز:
```powershell
.\fix_encoding.ps1
```

### إذا لم يعمل التطبيق:
1. تأكد من تشغيل محاكي Android
2. تحقق من تثبيت Flutter: `flutter doctor`
3. نظف المشروع: `flutter clean`
4. أعد المحاولة

## ما ستراه عند التشغيل

### الشاشة الرئيسية
- قائمة الفواتير (فارغة في البداية)
- إحصائيات المبالغ
- زر إضافة فاتورة جديدة (+)

### إنشاء فاتورة جديدة
1. اضغط على زر (+) الأزرق
2. أضف معلومات الأعمال
3. أضف معلومات العميل  
4. أضف العناصر (منتجات/خدمات)
5. اختر الإعدادات (لغة، قالب، عملة)
6. معاينة وحفظ الفاتورة

## الميزات المتاحة

✅ **إنشاء فواتير احترافية**
✅ **6 قوالب مختلفة**
✅ **دعم 10+ لغات**
✅ **دعم 20+ عملة**
✅ **حسابات تلقائية للضرائب والخصومات**
✅ **توقيعات رقمية**
✅ **شروط وأحكام قابلة للتخصيص**
✅ **8 طرق دفع مختلفة**

---

**💡 نصيحة**: للحصول على أفضل تجربة، استخدم جهاز Android حقيقي بدلاً من المحاكي.
