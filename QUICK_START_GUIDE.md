# 🚀 Quick Start Guide - Invoice Application

## 📱 **Getting Started**

### 1. **First Launch**
- The app will start with the Invoice List screen
- Bottom navigation provides access to all main features:
  - ⚙️ Settings
  - 📦 Items (Products/Services)
  - 👥 Customers
  - 📊 Estimates (Coming Soon)
  - 🧾 Invoices

### 2. **Initial Setup**
1. **Configure Settings** (⚙️ Settings tab):
   - Set your default currency (SAR, USD, etc.)
   - Configure business information
   - Set tax rates and payment methods
   - Choose date and number formats

2. **Add Business Information**:
   - Go to Settings → Business Info
   - Enter your company details
   - Add contact information
   - Set up payment terms

### 3. **Add Customers** (👥 Customers tab):
   - Tap the + button to add new customers
   - Fill in customer details (name, email, phone, address)
   - Add company information if needed
   - Save customer for future use

### 4. **Add Products/Services** (📦 Items tab):
   - Tap the + button to add new items
   - Enter product/service name and description
   - Set pricing and units
   - Choose categories for organization
   - Configure tax rates per item

## 🧾 **Creating Your First Invoice**

### Step 1: Start New Invoice
- Go to Invoices tab (🧾)
- Tap the + (floating action button)
- The invoice creation wizard will open

### Step 2: Business Information
- Your business info will auto-populate from settings
- Modify if needed for this specific invoice
- Tap "Next" to continue

### Step 3: Customer Information
- Select existing customer or add new one
- Fill in customer details
- Tap "Next" to continue

### Step 4: Add Items
- Select from your products/services catalog
- Or add custom items for this invoice
- Set quantities and prices
- Items will calculate totals automatically

### Step 5: Review & Calculations
- Review all invoice details
- Adjust discounts, taxes, shipping if needed
- Check totals and calculations
- Add notes or terms if required

### Step 6: Template & Language
- Choose invoice template (Modern, Classic, etc.)
- Select language (Arabic/English)
- Set currency display preferences

### Step 7: Final Details
- Add payment method information
- Set due date and terms
- Add signature if required
- Review everything one final time

### Step 8: Save & Share
- Save the invoice to database
- Generate PDF for sharing
- Share via email, messaging, or other apps
- Print if needed

## 📊 **Dashboard Features**

### Statistics Cards
- **Total Overdue**: Shows overdue invoices and amounts
- **Total Unpaid**: Shows all unpaid invoices and amounts
- Numbers update automatically based on your data

### Filtering Options
- **All**: Show all invoices
- **Overdue**: Show only overdue invoices
- **Partially Paid**: Show partially paid invoices
- **Unpaid**: Show all unpaid invoices

### Invoice Actions
- **Tap invoice**: View formatted invoice preview
- **Edit**: Modify invoice details
- **Share**: Generate and share PDF
- **Mark as Paid**: Update payment status

## ⚙️ **Settings Overview**

### General Settings
- **Currency**: Default currency for new invoices
- **Number Format**: How numbers are displayed
- **Date Format**: Date display preferences
- **Language**: Interface language

### Business Settings
- **Business Info**: Company details and contact info
- **Tax Rate**: Default tax percentage
- **Payment Method**: Default payment terms
- **Terms & Conditions**: Standard invoice terms

### Invoice Settings
- **Due Terms**: Default payment due period
- **Templates**: Choose default invoice template
- **Show Paid**: Display payment status on invoices

## 🔧 **Tips & Best Practices**

### 1. **Data Organization**
- Set up customers and products before creating invoices
- Use categories to organize your products/services
- Keep business information up to date

### 2. **Invoice Management**
- Use clear, descriptive invoice numbers
- Set appropriate due dates
- Add detailed item descriptions
- Include payment terms and methods

### 3. **Professional Appearance**
- Choose appropriate templates for your business
- Ensure business information is complete
- Use consistent branding elements
- Include all necessary legal information

### 4. **Data Backup**
- Regularly export important invoices as PDFs
- Keep backup copies of customer and product data
- Consider cloud storage for important documents

## 🆘 **Troubleshooting**

### Common Issues:
1. **PDF Generation Fails**: Check storage permissions
2. **Data Not Saving**: Ensure sufficient device storage
3. **Sharing Not Working**: Check app permissions
4. **Templates Not Loading**: Restart the application

### Getting Help:
- Check the settings screen for app version
- Use the feedback option in settings
- Refer to this guide for common procedures

## 🎯 **Next Steps**

Once you're comfortable with the basics:
1. Explore different invoice templates
2. Set up recurring customers and products
3. Use filtering to manage large invoice lists
4. Customize settings for your business needs
5. Explore PDF sharing and printing options

**Happy Invoicing! 🎉**
