# Invoice App

A comprehensive Flutter invoice management application with clean architecture, featuring complete invoice creation, preview, and management capabilities.

## Features

### 📋 Invoice Management
- **Create New Invoices**: Complete invoice creation workflow with all necessary fields
- **Invoice List**: View and manage all invoices with filtering options
- **Invoice Preview**: Professional invoice preview with PDF-like layout
- **Multiple Templates**: Choose from 6 different professional invoice templates
- **Multi-language Support**: Support for 10+ languages including Arabic, English, French, etc.

### 💼 Business & Client Management
- **Business Information**: Add and manage complete business details with logo support
- **Client Information**: Comprehensive client management with contact details
- **Contact Management**: Store and organize business and client information

### 🧮 Advanced Calculations
- **Item Management**: Add unlimited items with descriptions, quantities, and prices
- **Real-time Calculations**: Automatic subtotal, tax, discount, and total calculations
- **Flexible Tax & Discount**: Support for both percentage and fixed amount calculations
- **Shipping Costs**: Add shipping charges to invoices
- **Multi-Currency Support**: Support for 20+ currencies including SAR, USD, EUR, etc.

### 🎨 Customization & Branding
- **Professional Templates**: Modern, Classic, Minimal, Creative, Corporate, and Service templates
- **Digital Signatures**: Add text signatures with preview functionality
- **Terms & Conditions**: Customizable terms with pre-built templates
- **Payment Methods**: 8 different payment method options
- **Currency Selection**: Comprehensive currency support with symbols

### 📱 User Experience
- **Modern UI**: Clean and intuitive Material Design 3 interface
- **Responsive Design**: Optimized for different screen sizes
- **Real-time Updates**: Live calculation updates as you type
- **Form Validation**: Comprehensive input validation with helpful error messages
- **Smooth Navigation**: Seamless flow between different screens

## Getting Started

### Prerequisites

- Flutter SDK (3.0.0 or higher)
- Dart SDK (3.0.0 or higher)
- Android Studio / VS Code with Flutter extensions

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/invoice.git
```

2. Navigate to the project directory:
```bash
cd invoice
```

3. Install dependencies:
```bash
flutter pub get
```

4. Generate required files:
```bash
flutter pub run build_runner build --delete-conflicting-outputs
```

5. Run the app:
```bash
flutter run
```

## Usage

### Creating Your First Invoice

1. **Launch the App**: Open the invoice app and you'll see the main invoice list screen
2. **Create New Invoice**: Tap the blue "+" floating action button
3. **Add Business Information**:
   - Tap "Business Info"
   - Fill in your business name, email, phone, and address
   - Optionally add website and tax number
   - Tap "Save"
4. **Add Client Information**:
   - Tap "Client Info"
   - Enter client name, email, phone, and address
   - Optionally add company name and tax number
   - Tap "Save"
5. **Add Invoice Items**:
   - Tap "Add Item" in the Items section
   - Enter item name, description, quantity, and unit price
   - The total will be calculated automatically
   - Tap "Save" to add the item
   - Repeat for additional items
6. **Configure Invoice Settings**:
   - **Language**: Choose from 10+ supported languages
   - **Template**: Select from 6 professional templates
   - **Currency**: Choose from 20+ supported currencies
   - **Calculations**: Add discounts, taxes, and shipping costs
   - **Signature**: Add your digital signature
   - **Terms**: Add terms and conditions using templates or custom text
   - **Payment Method**: Select preferred payment method
7. **Preview & Save**:
   - Tap "Preview" to see the final invoice layout
   - Review all details and make adjustments if needed
   - Tap "Save" to store the invoice

### Managing Invoices

- **View All Invoices**: The main screen shows all your invoices
- **Filter Invoices**: Use the filter tabs (All, Unpaid, Partially Paid, Overdue)
- **Search Invoices**: Use the search icon to find specific invoices
- **Edit Invoices**: Tap on any invoice to edit its details
- **Track Totals**: View total overdue and unpaid amounts at the top

### Customization Options

- **Templates**: Choose from Modern, Classic, Minimal, Creative, Corporate, or Service templates
- **Languages**: Support for English, Arabic, French, Spanish, German, Italian, Portuguese, Russian, Chinese, and Japanese
- **Currencies**: Full support for major world currencies with proper symbols
- **Payment Methods**: Bank Transfer, Credit Card, PayPal, Cash, Check, Digital Wallet, Cryptocurrency, Online Banking

## Architecture

This project follows Clean Architecture principles and is organized into the following layers:

- **Domain**: Contains business logic and entities
- **Data**: Implements repositories and data sources
- **Presentation**: Contains UI components and state management

### Project Structure

```
lib/
  ├── core/
  │   ├── database/
  │   ├── di/
  │   └── error/
  ├── features/
  │   └── invoice/
  │       ├── data/
  │       │   ├── models/
  │       │   └── repositories/
  │       ├── domain/
  │       │   ├── entities/
  │       │   └── repositories/
  │       └── presentation/
  │           ├── bloc/
  │           ├── screens/
  │           └── widgets/
  └── main.dart
```

## Dependencies

- **flutter_bloc**: State management
- **get_it**: Dependency injection
- **sqflite**: SQLite database
- **freezed**: Code generation for data classes
- **uuid**: Unique ID generation
- **intl**: Internationalization and formatting

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
