@echo off
chcp 65001 >nul
title Invoice App Launcher

echo.
echo ========================================
echo         Invoice App Launcher
echo ========================================
echo.

cd /d "d:\invoice"

echo [1/5] Checking Flutter installation...
flutter doctor --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Flutter not found! Please install Flutter first.
    pause
    exit /b 1
)
echo ✓ Flutter found

echo.
echo [2/5] Installing dependencies...
call flutter pub get
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)
echo ✓ Dependencies installed

echo.
echo [3/5] Checking connected devices...
call flutter devices
echo ✓ Device check completed

echo.
echo [4/5] Building and running the app...
echo Make sure you have a device connected or emulator running!
echo.

call flutter run --verbose

echo.
echo [5/5] App execution finished.
echo.
pause
