# Fix encoding issues for Invoice App
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Set-Location "d:\invoice"

Write-Host "=== Fixing Encoding Issues ===" -ForegroundColor Cyan

# Set system locale to English
Write-Host "Setting system locale..." -ForegroundColor Green
$env:LANG = "en_US.UTF-8"
$env:LC_ALL = "en_US.UTF-8"

# Clean gradle cache
Write-Host "Cleaning Gradle cache..." -ForegroundColor Green
if (Test-Path "$env:USERPROFILE\.gradle") {
    Remove-Item "$env:USERPROFILE\.gradle\caches" -Recurse -Force -ErrorAction SilentlyContinue
}

# Clean Flutter cache
Write-Host "Cleaning Flutter cache..." -ForegroundColor Green
flutter clean

# Recreate Android files with correct encoding
Write-Host "Fixing Android configuration..." -ForegroundColor Green

# Update gradle.properties with correct encoding
$gradleProps = @"
org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -Duser.language=en -Duser.country=US
android.useAndroidX=true
android.enableJetifier=true
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
"@

$gradleProps | Out-File -FilePath "android\gradle.properties" -Encoding UTF8 -Force

Write-Host "✓ Encoding issues fixed!" -ForegroundColor Green
Write-Host "You can now run the app using:" -ForegroundColor Yellow
Write-Host "  .\quick_run.ps1" -ForegroundColor Cyan

Read-Host "Press Enter to continue"
