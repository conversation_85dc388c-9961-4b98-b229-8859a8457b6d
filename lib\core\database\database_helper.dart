import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseHelper {
  static const _databaseName = "invoice_app.db";
  static const _databaseVersion = 5;

  DatabaseHelper._();
  static final DatabaseHelper instance = DatabaseHelper._();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Invoice table
    await db.execute('''
      CREATE TABLE invoices (
        id TEXT PRIMARY KEY,
        invoice_number TEXT NOT NULL,
        creation_date TEXT NOT NULL,
        due_date TEXT NOT NULL,
        due_terms TEXT DEFAULT '7 days',
        po_number TEXT,
        invoice_title TEXT DEFAULT 'INVOICE',
        business_name TEXT NOT NULL,
        business_email TEXT NOT NULL,
        business_phone TEXT NOT NULL,
        business_address TEXT NOT NULL,
        business_website TEXT,
        business_tax_id TEXT,
        business_company TEXT,
        client_name TEXT NOT NULL,
        client_email TEXT NOT NULL,
        client_phone TEXT NOT NULL,
        client_address TEXT NOT NULL,
        client_company TEXT,
        client_tax_id TEXT,
        subtotal REAL NOT NULL DEFAULT 0.0,
        discount REAL NOT NULL DEFAULT 0.0,
        discount_type TEXT DEFAULT 'percentage',
        tax REAL NOT NULL DEFAULT 0.0,
        tax_type TEXT DEFAULT 'percentage',
        shipping REAL NOT NULL DEFAULT 0.0,
        total REAL NOT NULL DEFAULT 0.0,
        currency TEXT NOT NULL DEFAULT 'SAR',
        language TEXT DEFAULT 'English',
        template TEXT DEFAULT 'Modern',
        status TEXT NOT NULL DEFAULT 'unpaid',
        notes TEXT,
        terms TEXT,
        signature TEXT,
        payment_method TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Invoice items table
    await db.execute('''
      CREATE TABLE invoice_items (
        id TEXT PRIMARY KEY,
        invoice_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        quantity REAL NOT NULL DEFAULT 1.0,
        unit_price REAL NOT NULL DEFAULT 0.0,
        amount REAL NOT NULL DEFAULT 0.0,
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
      )
    ''');

    // Business info table for storing default business information
    await db.execute('''
      CREATE TABLE business_info (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        phone TEXT NOT NULL,
        address TEXT NOT NULL,
        website TEXT,
        tax_id TEXT,
        company TEXT,
        logo_path TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Settings table for app configuration
    await db.execute('''
      CREATE TABLE settings (
        id TEXT PRIMARY KEY,
        category TEXT NOT NULL,
        key TEXT NOT NULL,
        value TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Insert default settings
    await _insertDefaultSettings(db);

    // Client info table for storing client information
    await db.execute('''
      CREATE TABLE clients (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        phone TEXT NOT NULL,
        address TEXT NOT NULL,
        company TEXT,
        tax_id TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Items table for storing products and services
    await db.execute('''
      CREATE TABLE items (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL DEFAULT 'منتجات',
        unit_price REAL NOT NULL DEFAULT 0.0,
        unit TEXT NOT NULL DEFAULT 'قطعة',
        tax_rate REAL NOT NULL DEFAULT 0.0,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 4) {
      // Drop and recreate settings table to ensure it exists properly
      await db.execute('DROP TABLE IF EXISTS settings');

      // Create settings table
      await db.execute('''
        CREATE TABLE settings (
          id TEXT PRIMARY KEY,
          category TEXT NOT NULL,
          key TEXT NOT NULL,
          value TEXT NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      // Insert default settings
      await _insertDefaultSettings(db);
    }

    if (oldVersion < 5) {
      // Add items table for products and services
      await db.execute('''
        CREATE TABLE IF NOT EXISTS items (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          category TEXT NOT NULL DEFAULT 'منتجات',
          unit_price REAL NOT NULL DEFAULT 0.0,
          unit TEXT NOT NULL DEFAULT 'قطعة',
          tax_rate REAL NOT NULL DEFAULT 0.0,
          is_active INTEGER NOT NULL DEFAULT 1,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');
    }
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  // Delete and recreate database
  Future<void> resetDatabase() async {
    final dbPath = join(await getDatabasesPath(), _databaseName);
    await deleteDatabase(dbPath);
    _database = null; // Reset the cached database
  }

  // Insert default settings
  Future<void> _insertDefaultSettings(Database db) async {
    final now = DateTime.now().toIso8601String();

    final defaultSettings = [
      // General settings
      {
        'id': 'general_currency',
        'category': 'general',
        'key': 'default_currency',
        'value': 'SAR'
      },
      {
        'id': 'general_number_format',
        'category': 'general',
        'key': 'number_format',
        'value': '1,000,000.00'
      },
      {
        'id': 'general_date_format',
        'category': 'general',
        'key': 'date_format',
        'value': 'yyyy/MM/dd'
      },
      {
        'id': 'general_language',
        'category': 'general',
        'key': 'language',
        'value': 'Arabic'
      },

      // Business settings
      {
        'id': 'business_tax_rate',
        'category': 'business',
        'key': 'tax_rate',
        'value': '15'
      },
      {
        'id': 'business_tax_type',
        'category': 'business',
        'key': 'tax_type',
        'value': 'percentage'
      },
      {
        'id': 'business_payment_method',
        'category': 'business',
        'key': 'payment_method',
        'value': 'Bank Transfer'
      },
      {
        'id': 'business_terms',
        'category': 'business',
        'key': 'terms_conditions',
        'value': 'Payment is due within 30 days'
      },
      {
        'id': 'business_signature',
        'category': 'business',
        'key': 'signature',
        'value': ''
      },

      // Invoice settings
      {
        'id': 'invoice_due_terms',
        'category': 'invoice',
        'key': 'due_terms',
        'value': '7 days'
      },
      {
        'id': 'invoice_show_paid',
        'category': 'invoice',
        'key': 'show_paid_on_invoice',
        'value': 'true'
      },
      {
        'id': 'invoice_template',
        'category': 'invoice',
        'key': 'default_template',
        'value': 'Modern'
      },
    ];

    for (final setting in defaultSettings) {
      await db.insert(
        'settings',
        {
          'id': setting['id'],
          'category': setting['category'],
          'key': setting['key'],
          'value': setting['value'],
          'created_at': now,
          'updated_at': now,
        },
        conflictAlgorithm: ConflictAlgorithm.ignore,
      );
    }
  }

  // Settings CRUD operations
  Future<String?> getSetting(String key) async {
    final db = await database;
    final result = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: [key],
      limit: 1,
    );

    if (result.isNotEmpty) {
      return result.first['value'] as String;
    }
    return null;
  }

  Future<void> setSetting(String key, String value, String category) async {
    final db = await database;
    final now = DateTime.now().toIso8601String();

    await db.insert(
      'settings',
      {
        'id': '${category}_$key',
        'category': category,
        'key': key,
        'value': value,
        'created_at': now,
        'updated_at': now,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<Map<String, String>> getAllSettings() async {
    final db = await database;
    final result = await db.query('settings');

    final settings = <String, String>{};
    for (final row in result) {
      settings[row['key'] as String] = row['value'] as String;
    }

    return settings;
  }

  Future<Map<String, String>> getSettingsByCategory(String category) async {
    final db = await database;
    final result = await db.query(
      'settings',
      where: 'category = ?',
      whereArgs: [category],
    );

    final settings = <String, String>{};
    for (final row in result) {
      settings[row['key'] as String] = row['value'] as String;
    }

    return settings;
  }
}
