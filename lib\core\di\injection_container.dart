import 'package:get_it/get_it.dart';
import 'package:invoice/core/database/database_helper.dart';
import 'package:invoice/features/invoice/data/repositories/invoice_repository_impl.dart';
import 'package:invoice/features/invoice/domain/repositories/invoice_repository.dart';
import 'package:invoice/features/invoice/presentation/bloc/invoice_bloc.dart';
import 'package:uuid/uuid.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // Bloc
  sl.registerFactory(
    () => InvoiceBloc(repository: sl()),
  );

  // Repositories
  sl.registerLazySingleton<InvoiceRepository>(
    () => InvoiceRepositoryImpl(
      databaseHelper: sl(),
      uuid: sl(),
    ),
  );

  // Core
  sl.registerLazySingleton(() => DatabaseHelper.instance);
  sl.registerLazySingleton(() => const Uuid());
}
