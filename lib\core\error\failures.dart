import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  final String? code;

  const Failure({required this.message, this.code});

  @override
  List<Object?> get props => [message, code];
}

class DatabaseFailure extends Failure {
  const DatabaseFailure({required String message, String? code}) 
      : super(message: message, code: code);
}

class ValidationFailure extends Failure {
  const ValidationFailure({required String message, String? code}) 
      : super(message: message, code: code);
} 