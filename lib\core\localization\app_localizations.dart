import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static const List<Locale> supportedLocales = [
    Locale('en', ''), // English
    Locale('ar', ''), // Arabic
  ];

  bool get isArabic => locale.languageCode == 'ar';

  // Common UI strings
  String get appName => isArabic ? 'تطبيق الفواتير' : 'Invoice App';
  String get invoice => isArabic ? 'فاتورة' : 'Invoice';
  String get invoices => isArabic ? 'الفواتير' : 'Invoices';
  String get customer => isArabic ? 'العميل' : 'Customer';
  String get customers => isArabic ? 'العملاء' : 'Customers';
  String get product => isArabic ? 'المنتج' : 'Product';
  String get products => isArabic ? 'المنتجات' : 'Products';
  String get settings => isArabic ? 'الإعدادات' : 'Settings';
  String get save => isArabic ? 'حفظ' : 'Save';
  String get cancel => isArabic ? 'إلغاء' : 'Cancel';
  String get delete => isArabic ? 'حذف' : 'Delete';
  String get edit => isArabic ? 'تعديل' : 'Edit';
  String get add => isArabic ? 'إضافة' : 'Add';
  String get create => isArabic ? 'إنشاء' : 'Create';
  String get update => isArabic ? 'تحديث' : 'Update';
  String get search => isArabic ? 'بحث' : 'Search';
  String get filter => isArabic ? 'تصفية' : 'Filter';
  String get all => isArabic ? 'الكل' : 'All';
  String get name => isArabic ? 'الاسم' : 'Name';
  String get email => isArabic ? 'البريد الإلكتروني' : 'Email';
  String get phone => isArabic ? 'الهاتف' : 'Phone';
  String get address => isArabic ? 'العنوان' : 'Address';
  String get company => isArabic ? 'الشركة' : 'Company';
  String get description => isArabic ? 'الوصف' : 'Description';
  String get quantity => isArabic ? 'الكمية' : 'Quantity';
  String get price => isArabic ? 'السعر' : 'Price';
  String get total => isArabic ? 'المجموع' : 'Total';
  String get subtotal => isArabic ? 'المجموع الفرعي' : 'Subtotal';
  String get tax => isArabic ? 'الضريبة' : 'Tax';
  String get discount => isArabic ? 'الخصم' : 'Discount';
  String get shipping => isArabic ? 'الشحن' : 'Shipping';
  String get date => isArabic ? 'التاريخ' : 'Date';
  String get dueDate => isArabic ? 'تاريخ الاستحقاق' : 'Due Date';
  String get status => isArabic ? 'الحالة' : 'Status';
  String get paid => isArabic ? 'مدفوع' : 'Paid';
  String get unpaid => isArabic ? 'غير مدفوع' : 'Unpaid';
  String get overdue => isArabic ? 'متأخر' : 'Overdue';
  String get partiallyPaid => isArabic ? 'مدفوع جزئياً' : 'Partially Paid';
  String get draft => isArabic ? 'مسودة' : 'Draft';

  // Navigation
  String get home => isArabic ? 'الرئيسية' : 'Home';
  String get dashboard => isArabic ? 'لوحة التحكم' : 'Dashboard';

  // Invoice specific
  String get invoiceNumber => isArabic ? 'رقم الفاتورة' : 'Invoice Number';
  String get createInvoice => isArabic ? 'إنشاء فاتورة' : 'Create Invoice';
  String get editInvoice => isArabic ? 'تعديل الفاتورة' : 'Edit Invoice';
  String get deleteInvoice => isArabic ? 'حذف الفاتورة' : 'Delete Invoice';
  String get shareInvoice => isArabic ? 'مشاركة الفاتورة' : 'Share Invoice';
  String get printInvoice => isArabic ? 'طباعة الفاتورة' : 'Print Invoice';
  String get downloadPDF => isArabic ? 'تحميل PDF' : 'Download PDF';
  String get businessInfo => isArabic ? 'معلومات الشركة' : 'Business Info';
  String get clientInfo => isArabic ? 'معلومات العميل' : 'Client Info';
  String get items => isArabic ? 'العناصر' : 'Items';
  String get addItem => isArabic ? 'إضافة عنصر' : 'Add Item';
  String get removeItem => isArabic ? 'إزالة العنصر' : 'Remove Item';
  String get template => isArabic ? 'القالب' : 'Template';
  String get selectTemplate => isArabic ? 'اختر القالب' : 'Select Template';
  String get modern => isArabic ? 'حديث' : 'Modern';
  String get classic => isArabic ? 'كلاسيكي' : 'Classic';
  String get minimal => isArabic ? 'بسيط' : 'Minimal';
  String get creative => isArabic ? 'إبداعي' : 'Creative';
  String get corporate => isArabic ? 'مؤسسي' : 'Corporate';
  String get service => isArabic ? 'خدمة' : 'Service';

  // Customer management
  String get addCustomer => isArabic ? 'إضافة عميل' : 'Add Customer';
  String get editCustomer => isArabic ? 'تعديل العميل' : 'Edit Customer';
  String get deleteCustomer => isArabic ? 'حذف العميل' : 'Delete Customer';
  String get customerName => isArabic ? 'اسم العميل' : 'Customer Name';
  String get customerEmail => isArabic ? 'بريد العميل' : 'Customer Email';
  String get customerPhone => isArabic ? 'هاتف العميل' : 'Customer Phone';
  String get customerAddress => isArabic ? 'عنوان العميل' : 'Customer Address';
  String get customerCompany => isArabic ? 'شركة العميل' : 'Customer Company';

  // Product management
  String get addProduct => isArabic ? 'إضافة منتج' : 'Add Product';
  String get editProduct => isArabic ? 'تعديل المنتج' : 'Edit Product';
  String get deleteProduct => isArabic ? 'حذف المنتج' : 'Delete Product';
  String get productName => isArabic ? 'اسم المنتج' : 'Product Name';
  String get productDescription => isArabic ? 'وصف المنتج' : 'Product Description';
  String get productPrice => isArabic ? 'سعر المنتج' : 'Product Price';
  String get category => isArabic ? 'الفئة' : 'Category';
  String get unit => isArabic ? 'الوحدة' : 'Unit';

  // Settings
  String get generalSettings => isArabic ? 'الإعدادات العامة' : 'General Settings';
  String get businessSettings => isArabic ? 'إعدادات الشركة' : 'Business Settings';
  String get invoiceSettings => isArabic ? 'إعدادات الفاتورة' : 'Invoice Settings';
  String get language => isArabic ? 'اللغة' : 'Language';
  String get currency => isArabic ? 'العملة' : 'Currency';
  String get taxRate => isArabic ? 'معدل الضريبة' : 'Tax Rate';
  String get paymentMethod => isArabic ? 'طريقة الدفع' : 'Payment Method';
  String get termsAndConditions => isArabic ? 'الشروط والأحكام' : 'Terms and Conditions';
  String get signature => isArabic ? 'التوقيع' : 'Signature';

  // Messages
  String get success => isArabic ? 'نجح' : 'Success';
  String get error => isArabic ? 'خطأ' : 'Error';
  String get warning => isArabic ? 'تحذير' : 'Warning';
  String get info => isArabic ? 'معلومات' : 'Info';
  String get loading => isArabic ? 'جاري التحميل...' : 'Loading...';
  String get noData => isArabic ? 'لا توجد بيانات' : 'No data';
  String get noInvoices => isArabic ? 'لا توجد فواتير' : 'No invoices';
  String get noCustomers => isArabic ? 'لا يوجد عملاء' : 'No customers';
  String get noProducts => isArabic ? 'لا توجد منتجات' : 'No products';

  // Validation messages
  String get fieldRequired => isArabic ? 'هذا الحقل مطلوب' : 'This field is required';
  String get invalidEmail => isArabic ? 'بريد إلكتروني غير صحيح' : 'Invalid email';
  String get invalidPhone => isArabic ? 'رقم هاتف غير صحيح' : 'Invalid phone number';
  String get invalidAmount => isArabic ? 'مبلغ غير صحيح' : 'Invalid amount';

  // Actions
  String get confirm => isArabic ? 'تأكيد' : 'Confirm';
  String get yes => isArabic ? 'نعم' : 'Yes';
  String get no => isArabic ? 'لا' : 'No';
  String get ok => isArabic ? 'موافق' : 'OK';
  String get close => isArabic ? 'إغلاق' : 'Close';
  String get back => isArabic ? 'رجوع' : 'Back';
  String get next => isArabic ? 'التالي' : 'Next';
  String get previous => isArabic ? 'السابق' : 'Previous';
  String get finish => isArabic ? 'إنهاء' : 'Finish';

  // Time and dates
  String get today => isArabic ? 'اليوم' : 'Today';
  String get yesterday => isArabic ? 'أمس' : 'Yesterday';
  String get tomorrow => isArabic ? 'غداً' : 'Tomorrow';
  String get thisWeek => isArabic ? 'هذا الأسبوع' : 'This Week';
  String get thisMonth => isArabic ? 'هذا الشهر' : 'This Month';
  String get thisYear => isArabic ? 'هذا العام' : 'This Year';

  // Units
  String get piece => isArabic ? 'قطعة' : 'Piece';
  String get hour => isArabic ? 'ساعة' : 'Hour';
  String get day => isArabic ? 'يوم' : 'Day';
  String get month => isArabic ? 'شهر' : 'Month';
  String get year => isArabic ? 'سنة' : 'Year';
  String get kg => isArabic ? 'كيلو' : 'Kg';
  String get meter => isArabic ? 'متر' : 'Meter';
  String get liter => isArabic ? 'لتر' : 'Liter';

  // Currencies
  String get sar => isArabic ? 'ريال سعودي' : 'SAR';
  String get usd => isArabic ? 'دولار أمريكي' : 'USD';
  String get eur => isArabic ? 'يورو' : 'EUR';
  String get aed => isArabic ? 'درهم إماراتي' : 'AED';
  String get egp => isArabic ? 'جنيه مصري' : 'EGP';
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
