import 'package:flutter/material.dart';
import 'package:invoice/features/invoice/presentation/screens/invoice_list_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/customer_management_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/item_management_screen.dart';
import 'package:invoice/features/settings/presentation/screens/settings_screen.dart';

class MainNavigationWrapper extends StatefulWidget {
  const MainNavigationWrapper({super.key});

  @override
  State<MainNavigationWrapper> createState() => _MainNavigationWrapperState();
}

class _MainNavigationWrapperState extends State<MainNavigationWrapper> {
  int _selectedIndex = 4; // Start with Invoices tab (index 4)

  // Navigation keys for each tab to maintain separate navigation stacks
  final List<GlobalKey<NavigatorState>> _navigatorKeys = [
    GlobalKey<NavigatorState>(), // Settings
    GlobalKey<NavigatorState>(), // Items
    GlobalKey<NavigatorState>(), // Customers
    GlobalKey<NavigatorState>(), // Estimates
    GlobalKey<NavigatorState>(), // Invoices
  ];

  void _onItemTapped(int index) {
    if (_selectedIndex == index) {
      // If tapping the same tab, pop to root of that tab
      _navigatorKeys[index].currentState?.popUntil((route) => route.isFirst);
    } else {
      setState(() {
        _selectedIndex = index;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        // Handle back button press - pop from current tab's navigator
        final isFirstRouteInCurrentTab =
            !await _navigatorKeys[_selectedIndex].currentState!.maybePop();

        if (isFirstRouteInCurrentTab) {
          // If we're on the first route of current tab, switch to invoices tab
          if (_selectedIndex != 4) {
            setState(() {
              _selectedIndex = 4;
            });
            return;
          }
          // If already on invoices tab, allow system to handle back press
          if (mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        body: IndexedStack(
          index: _selectedIndex,
          children: [
            _buildNavigator(0, const SettingsScreen()),
            _buildNavigator(1, const ItemManagementScreen()),
            _buildNavigator(2, const CustomerManagementScreen()),
            _buildNavigator(3, const EstimateScreen()),
            _buildNavigator(4, const InvoiceListScreen()),
          ],
        ),
        bottomNavigationBar: _buildBottomNavigationBar(),
      ),
    );
  }

  Widget _buildNavigator(int index, Widget child) {
    return Navigator(
      key: _navigatorKeys[index],
      onGenerateRoute: (routeSettings) {
        return MaterialPageRoute(
          builder: (context) => child,
          settings: routeSettings,
        );
      },
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        selectedItemColor: const Color(0xFF4285F4),
        unselectedItemColor: Colors.grey,
        backgroundColor: Colors.white,
        elevation:
            0, // Remove default elevation since we're using custom shadow
        selectedFontSize: 12,
        unselectedFontSize: 12,
        iconSize: 24,
        onTap: _onItemTapped,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'الإعدادات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory_2),
            label: 'المنتجات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'العملاء',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.description),
            label: 'التقديرات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.receipt_long),
            label: 'الفواتير',
          ),
        ],
      ),
    );
  }
}

// Placeholder screen for estimates
class EstimateScreen extends StatelessWidget {
  const EstimateScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقديرات'),
        backgroundColor: const Color(0xFF4285F4),
        foregroundColor: Colors.white,
        automaticallyImplyLeading:
            false, // Remove back button since we have persistent navigation
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description,
              size: 80,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'صفحة التقديرات',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'قيد التطوير',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
