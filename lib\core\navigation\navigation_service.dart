import 'package:flutter/material.dart';

class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static NavigationService get instance => _instance;

  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  BuildContext? get context => navigatorKey.currentContext;

  Future<T?> navigateTo<T>(Widget screen) async {
    if (context == null) return null;
    
    return Navigator.push<T>(
      context!,
      MaterialPageRoute(builder: (_) => screen),
    );
  }

  Future<T?> navigateToReplacement<T>(Widget screen) async {
    if (context == null) return null;
    
    return Navigator.pushReplacement<T, void>(
      context!,
      MaterialPageRoute(builder: (_) => screen),
    );
  }

  void goBack<T>([T? result]) {
    if (context == null) return;
    Navigator.pop(context!, result);
  }

  void goBackToRoot() {
    if (context == null) return;
    Navigator.popUntil(context!, (route) => route.isFirst);
  }

  Future<T?> showModalBottomSheet<T>({
    required Widget child,
    bool isScrollControlled = false,
    bool isDismissible = true,
    bool enableDrag = true,
  }) async {
    if (context == null) return null;
    
    return showModalBottomSheet<T>(
      context: context!,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      builder: (_) => child,
    );
  }

  Future<T?> showDialog<T>({
    required Widget dialog,
    bool barrierDismissible = true,
  }) async {
    if (context == null) return null;
    
    return showDialog<T>(
      context: context!,
      barrierDismissible: barrierDismissible,
      builder: (_) => dialog,
    );
  }
}
