import 'package:flutter/material.dart';
import 'package:invoice/features/invoice/presentation/screens/invoice_list_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/create_invoice_screen.dart';

class AppRoutes {
  static const String invoiceList = '/invoice-list';
  static const String createInvoice = '/create-invoice';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case invoiceList:
        return MaterialPageRoute(
          builder: (_) => const InvoiceListScreen(),
        );
      case createInvoice:
        return MaterialPageRoute(
          builder: (_) => const CreateInvoiceScreen(),
        );
      default:
        return MaterialPageRoute(
          builder: (_) => const InvoiceListScreen(),
        );
    }
  }
}
