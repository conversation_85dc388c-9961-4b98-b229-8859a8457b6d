import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class ArabicFontService {
  static final ArabicFontService _instance = ArabicFontService._internal();
  factory ArabicFontService() => _instance;
  ArabicFontService._internal();

  static ArabicFontService get instance => _instance;

  pw.Font? _arabicFont;
  pw.Font? _regularFont;

  Future<void> initialize() async {
    await _loadFonts();
  }

  Future<void> _loadFonts() async {
    try {
      // Try to load Arabic fonts
      _arabicFont = await _loadArabicFont();
      _regularFont = pw.Font.helvetica();

      debugPrint('✅ Arabic font service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing Arabic font service: $e');
      _arabicFont = pw.Font.helvetica();
      _regularFont = pw.Font.helvetica();
    }
  }

  Future<pw.Font> _loadArabicFont() async {
    // List of Arabic font files to try
    final arabicFontPaths = [
      'assets/fonts/NotoSansArabic-Regular.ttf',
      'assets/fonts/Cairo-Regular.ttf',
      'assets/fonts/Amiri-Regular.ttf',
    ];

    for (final fontPath in arabicFontPaths) {
      try {
        final fontData = await rootBundle.load(fontPath);
        debugPrint('✅ Loaded Arabic font: $fontPath');
        return pw.Font.ttf(fontData);
      } catch (e) {
        debugPrint('⚠️ Could not load font $fontPath: $e');
        continue;
      }
    }

    // If no Arabic fonts found, create a basic Arabic-compatible font
    debugPrint('! No Arabic font files found, using system fallback');
    return _createFallbackArabicFont();
  }

  pw.Font _createFallbackArabicFont() {
    // For now, return Helvetica as fallback
    // In a real implementation, you might want to download fonts or use system fonts
    return pw.Font.helvetica();
  }

  // Helper method to detect Arabic text
  bool containsArabic(String text) {
    return RegExp(
            r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]')
        .hasMatch(text);
  }

  // Get appropriate font for text
  pw.Font getFontForText(String text) {
    if (containsArabic(text)) {
      return _arabicFont ?? pw.Font.helvetica();
    }
    return _regularFont ?? pw.Font.helvetica();
  }

  // Create text with proper font and direction
  pw.Text createText(
    String text, {
    double fontSize = 12,
    pw.FontWeight? fontWeight,
    PdfColor? color,
    pw.TextAlign? textAlign,
  }) {
    final font = getFontForText(text);
    final isArabic = containsArabic(text);

    return pw.Text(
      text,
      style: pw.TextStyle(
        font: font,
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      ),
      textAlign:
          textAlign ?? (isArabic ? pw.TextAlign.right : pw.TextAlign.left),
      textDirection: isArabic ? pw.TextDirection.rtl : pw.TextDirection.ltr,
    );
  }

  // Create rich text with mixed content
  pw.RichText createRichText(
    List<pw.TextSpan> spans, {
    pw.TextAlign? textAlign,
    pw.TextDirection? textDirection,
  }) {
    // Detect overall text direction based on first span with Arabic content
    pw.TextDirection direction = pw.TextDirection.ltr;
    for (final span in spans) {
      if (span.text != null && containsArabic(span.text!)) {
        direction = pw.TextDirection.rtl;
        break;
      }
    }

    return pw.RichText(
      text: pw.TextSpan(children: spans),
      textAlign: textAlign ??
          (direction == pw.TextDirection.rtl
              ? pw.TextAlign.right
              : pw.TextAlign.left),
      textDirection: textDirection ?? direction,
    );
  }

  // Create text span with proper font
  pw.TextSpan createTextSpan(
    String text, {
    double fontSize = 12,
    pw.FontWeight? fontWeight,
    PdfColor? color,
  }) {
    final font = getFontForText(text);

    return pw.TextSpan(
      text: text,
      style: pw.TextStyle(
        font: font,
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      ),
    );
  }

  // Format currency with proper direction
  String formatCurrency(double amount, String currency,
      {bool isArabic = false}) {
    final formattedAmount = amount.toStringAsFixed(2);

    if (isArabic) {
      // Arabic: amount + currency
      return '$formattedAmount $currency';
    } else {
      // English: currency + amount
      return '$currency $formattedAmount';
    }
  }

  // Format date with proper direction
  String formatDate(DateTime date, {bool isArabic = false}) {
    if (isArabic) {
      // Arabic format: DD/MM/YYYY
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    } else {
      // English format: MM/DD/YYYY
      return '${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}/${date.year}';
    }
  }

  // Get text direction for mixed content
  pw.TextDirection getTextDirection(String text) {
    return containsArabic(text) ? pw.TextDirection.rtl : pw.TextDirection.ltr;
  }

  // Clean up resources
  void dispose() {
    _arabicFont = null;
    _regularFont = null;
  }
}
