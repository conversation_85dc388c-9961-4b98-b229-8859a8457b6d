import 'package:intl/intl.dart';
import 'package:invoice/core/services/language_service.dart';

class ArabicFormattingService {
  static final ArabicFormattingService _instance = ArabicFormattingService._internal();
  factory ArabicFormattingService() => _instance;
  ArabicFormattingService._internal();

  static ArabicFormattingService get instance => _instance;

  // Arabic-Indic numerals mapping
  static const Map<String, String> _arabicNumerals = {
    '0': '٠',
    '1': '١',
    '2': '٢',
    '3': '٣',
    '4': '٤',
    '5': '٥',
    '6': '٦',
    '7': '٧',
    '8': '٨',
    '9': '٩',
  };

  static const Map<String, String> _latinNumerals = {
    '٠': '0',
    '١': '1',
    '٢': '2',
    '٣': '3',
    '٤': '4',
    '٥': '5',
    '٦': '6',
    '٧': '7',
    '٨': '8',
    '٩': '9',
  };

  // Arabic month names
  static const List<String> _arabicMonths = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];

  static const List<String> _arabicMonthsShort = [
    'ينا', 'فبر', 'مار', 'أبر', 'ماي', 'يون',
    'يول', 'أغس', 'سبت', 'أكت', 'نوف', 'ديس'
  ];

  // Arabic day names
  static const List<String> _arabicDays = [
    'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'
  ];

  static const List<String> _arabicDaysShort = [
    'أحد', 'اثن', 'ثلا', 'أرب', 'خمي', 'جمع', 'سبت'
  ];

  // Number formatting
  String formatNumber(num number, {bool useArabicNumerals = true}) {
    final languageService = LanguageService.instance;
    
    if (languageService.currentLanguage == AppLanguage.arabic && useArabicNumerals) {
      return _convertToArabicNumerals(number.toString());
    }
    
    return number.toString();
  }

  String formatInteger(int number, {bool useArabicNumerals = true}) {
    return formatNumber(number, useArabicNumerals: useArabicNumerals);
  }

  String formatDouble(double number, {int decimalPlaces = 2, bool useArabicNumerals = true}) {
    final formatted = number.toStringAsFixed(decimalPlaces);
    
    if (LanguageService.instance.currentLanguage == AppLanguage.arabic && useArabicNumerals) {
      return _convertToArabicNumerals(formatted);
    }
    
    return formatted;
  }

  String formatCurrency(double amount, String currency, {bool useArabicNumerals = true}) {
    final languageService = LanguageService.instance;
    final formattedAmount = formatDouble(amount, decimalPlaces: 2, useArabicNumerals: useArabicNumerals);
    
    if (languageService.currentLanguage == AppLanguage.arabic) {
      // Arabic: amount + currency
      return '$formattedAmount $currency';
    } else {
      // English: currency + amount
      return '$currency $formattedAmount';
    }
  }

  String formatPercentage(double percentage, {int decimalPlaces = 1, bool useArabicNumerals = true}) {
    final formatted = formatDouble(percentage, decimalPlaces: decimalPlaces, useArabicNumerals: useArabicNumerals);
    return '$formatted%';
  }

  // Date formatting
  String formatDate(DateTime date, {String? pattern, bool useArabicNumerals = true}) {
    final languageService = LanguageService.instance;
    
    if (languageService.currentLanguage == AppLanguage.arabic) {
      return _formatArabicDate(date, pattern: pattern, useArabicNumerals: useArabicNumerals);
    } else {
      final formatter = DateFormat(pattern ?? 'MMM dd, yyyy');
      return formatter.format(date);
    }
  }

  String formatShortDate(DateTime date, {bool useArabicNumerals = true}) {
    return formatDate(date, pattern: 'dd/MM/yyyy', useArabicNumerals: useArabicNumerals);
  }

  String formatLongDate(DateTime date, {bool useArabicNumerals = true}) {
    return formatDate(date, pattern: 'EEEE، dd MMMM yyyy', useArabicNumerals: useArabicNumerals);
  }

  String formatTime(DateTime time, {bool use24Hour = true, bool useArabicNumerals = true}) {
    final pattern = use24Hour ? 'HH:mm' : 'hh:mm a';
    final formatter = DateFormat(pattern);
    final formatted = formatter.format(time);
    
    if (LanguageService.instance.currentLanguage == AppLanguage.arabic && useArabicNumerals) {
      return _convertToArabicNumerals(formatted);
    }
    
    return formatted;
  }

  String formatDateTime(DateTime dateTime, {bool useArabicNumerals = true}) {
    final date = formatShortDate(dateTime, useArabicNumerals: useArabicNumerals);
    final time = formatTime(dateTime, useArabicNumerals: useArabicNumerals);
    
    if (LanguageService.instance.currentLanguage == AppLanguage.arabic) {
      return '$date في $time';
    } else {
      return '$date at $time';
    }
  }

  // Relative time formatting
  String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    final languageService = LanguageService.instance;
    final isArabic = languageService.currentLanguage == AppLanguage.arabic;
    
    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return isArabic ? 'أمس' : 'Yesterday';
      } else if (difference.inDays < 7) {
        return isArabic ? 'منذ ${formatNumber(difference.inDays)} أيام' : '${difference.inDays} days ago';
      } else if (difference.inDays < 30) {
        final weeks = (difference.inDays / 7).floor();
        return isArabic ? 'منذ ${formatNumber(weeks)} أسابيع' : '$weeks weeks ago';
      } else if (difference.inDays < 365) {
        final months = (difference.inDays / 30).floor();
        return isArabic ? 'منذ ${formatNumber(months)} أشهر' : '$months months ago';
      } else {
        final years = (difference.inDays / 365).floor();
        return isArabic ? 'منذ ${formatNumber(years)} سنوات' : '$years years ago';
      }
    } else if (difference.inHours > 0) {
      return isArabic ? 'منذ ${formatNumber(difference.inHours)} ساعات' : '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return isArabic ? 'منذ ${formatNumber(difference.inMinutes)} دقائق' : '${difference.inMinutes} minutes ago';
    } else {
      return isArabic ? 'الآن' : 'Now';
    }
  }

  // Private helper methods
  String _convertToArabicNumerals(String text) {
    String result = text;
    _arabicNumerals.forEach((latin, arabic) {
      result = result.replaceAll(latin, arabic);
    });
    return result;
  }

  String _convertToLatinNumerals(String text) {
    String result = text;
    _latinNumerals.forEach((arabic, latin) {
      result = result.replaceAll(arabic, latin);
    });
    return result;
  }

  String _formatArabicDate(DateTime date, {String? pattern, bool useArabicNumerals = true}) {
    // Default Arabic date pattern
    pattern ??= 'dd MMMM yyyy';
    
    String result = pattern;
    
    // Replace day
    result = result.replaceAll('dd', date.day.toString().padLeft(2, '0'));
    result = result.replaceAll('d', date.day.toString());
    
    // Replace month
    if (result.contains('MMMM')) {
      result = result.replaceAll('MMMM', _arabicMonths[date.month - 1]);
    } else if (result.contains('MMM')) {
      result = result.replaceAll('MMM', _arabicMonthsShort[date.month - 1]);
    } else if (result.contains('MM')) {
      result = result.replaceAll('MM', date.month.toString().padLeft(2, '0'));
    } else if (result.contains('M')) {
      result = result.replaceAll('M', date.month.toString());
    }
    
    // Replace year
    result = result.replaceAll('yyyy', date.year.toString());
    result = result.replaceAll('yy', (date.year % 100).toString().padLeft(2, '0'));
    
    // Replace day of week
    if (result.contains('EEEE')) {
      result = result.replaceAll('EEEE', _arabicDays[date.weekday % 7]);
    } else if (result.contains('EEE')) {
      result = result.replaceAll('EEE', _arabicDaysShort[date.weekday % 7]);
    }
    
    if (useArabicNumerals) {
      result = _convertToArabicNumerals(result);
    }
    
    return result;
  }

  // Utility methods for parsing
  double? parseArabicNumber(String text) {
    final latinText = _convertToLatinNumerals(text);
    return double.tryParse(latinText);
  }

  int? parseArabicInteger(String text) {
    final latinText = _convertToLatinNumerals(text);
    return int.tryParse(latinText);
  }

  // Text direction helpers
  bool isArabicText(String text) {
    final arabicRegex = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    return arabicRegex.hasMatch(text);
  }

  String addDirectionMarkers(String text) {
    if (isArabicText(text)) {
      // Add RLM (Right-to-Left Mark) for Arabic text
      return '\u200F$text\u200F';
    } else {
      // Add LRM (Left-to-Right Mark) for Latin text
      return '\u200E$text\u200E';
    }
  }

  // Format file sizes
  String formatFileSize(int bytes) {
    final languageService = LanguageService.instance;
    final isArabic = languageService.currentLanguage == AppLanguage.arabic;
    
    if (bytes < 1024) {
      return isArabic ? '${formatNumber(bytes)} بايت' : '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return isArabic ? '${formatDouble(kb, decimalPlaces: 1)} كيلوبايت' : '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return isArabic ? '${formatDouble(mb, decimalPlaces: 1)} ميجابايت' : '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return isArabic ? '${formatDouble(gb, decimalPlaces: 1)} جيجابايت' : '${gb.toStringAsFixed(1)} GB';
    }
  }
}
