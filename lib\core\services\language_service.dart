import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum AppLanguage {
  english('en', 'English', 'English'),
  arabic('ar', 'العربية', 'Arabic');

  const AppLanguage(this.code, this.nativeName, this.englishName);

  final String code;
  final String nativeName;
  final String englishName;

  bool get isRTL => this == AppLanguage.arabic;
  
  TextDirection get textDirection => isRTL ? TextDirection.rtl : TextDirection.ltr;
  
  static AppLanguage fromCode(String code) {
    return AppLanguage.values.firstWhere(
      (lang) => lang.code == code,
      orElse: () => AppLanguage.english,
    );
  }
}

class LanguageService extends ChangeNotifier {
  static const String _languageKey = 'app_language';
  
  AppLanguage _currentLanguage = AppLanguage.english;
  SharedPreferences? _prefs;

  AppLanguage get currentLanguage => _currentLanguage;
  bool get isRTL => _currentLanguage.isRTL;
  TextDirection get textDirection => _currentLanguage.textDirection;
  String get languageCode => _currentLanguage.code;

  static LanguageService? _instance;
  static LanguageService get instance {
    _instance ??= LanguageService._();
    return _instance!;
  }

  LanguageService._();

  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadSavedLanguage();
  }

  Future<void> _loadSavedLanguage() async {
    final savedLanguageCode = _prefs?.getString(_languageKey);
    if (savedLanguageCode != null) {
      _currentLanguage = AppLanguage.fromCode(savedLanguageCode);
      notifyListeners();
    }
  }

  Future<void> changeLanguage(AppLanguage language) async {
    if (_currentLanguage != language) {
      _currentLanguage = language;
      await _prefs?.setString(_languageKey, language.code);
      notifyListeners();
    }
  }

  Future<void> toggleLanguage() async {
    final newLanguage = _currentLanguage == AppLanguage.english 
        ? AppLanguage.arabic 
        : AppLanguage.english;
    await changeLanguage(newLanguage);
  }

  // Font family selection based on language
  String get primaryFontFamily {
    switch (_currentLanguage) {
      case AppLanguage.arabic:
        return 'Cairo';
      case AppLanguage.english:
        return 'Roboto';
    }
  }

  String get secondaryFontFamily {
    switch (_currentLanguage) {
      case AppLanguage.arabic:
        return 'NotoSansArabic';
      case AppLanguage.english:
        return 'Roboto';
    }
  }

  // Text style helpers for consistent Arabic/English styling
  TextStyle getTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    bool useSecondaryFont = false,
  }) {
    return TextStyle(
      fontFamily: useSecondaryFont ? secondaryFontFamily : primaryFontFamily,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      // Adjust letter spacing for Arabic
      letterSpacing: _currentLanguage == AppLanguage.arabic ? 0.5 : null,
    );
  }

  // Directional padding helpers
  EdgeInsetsDirectional getDirectionalPadding({
    double start = 0,
    double top = 0,
    double end = 0,
    double bottom = 0,
  }) {
    return EdgeInsetsDirectional.fromSTEB(start, top, end, bottom);
  }

  // Alignment helpers
  AlignmentDirectional get alignmentStart => AlignmentDirectional.centerStart;
  AlignmentDirectional get alignmentEnd => AlignmentDirectional.centerEnd;
  
  TextAlign get textAlignStart => isRTL ? TextAlign.right : TextAlign.left;
  TextAlign get textAlignEnd => isRTL ? TextAlign.left : TextAlign.right;

  // Number formatting for Arabic
  String formatNumber(num number) {
    if (_currentLanguage == AppLanguage.arabic) {
      // Convert to Arabic-Indic numerals
      return number.toString().replaceAllMapped(
        RegExp(r'[0-9]'),
        (match) => _arabicNumerals[int.parse(match.group(0)!)],
      );
    }
    return number.toString();
  }

  static const List<String> _arabicNumerals = [
    '٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'
  ];

  // Currency formatting
  String formatCurrency(double amount, String currency) {
    final formattedAmount = amount.toStringAsFixed(2);
    
    if (_currentLanguage == AppLanguage.arabic) {
      final arabicAmount = formatNumber(amount);
      // Place currency after amount in Arabic
      return '$arabicAmount $currency';
    } else {
      // Place currency before amount in English
      return '$currency $formattedAmount';
    }
  }

  // Date formatting
  String formatDate(DateTime date) {
    if (_currentLanguage == AppLanguage.arabic) {
      // Arabic date format: DD/MM/YYYY
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    } else {
      // English date format: MM/DD/YYYY
      return '${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}/${date.year}';
    }
  }

  // Mixed content handling
  bool containsArabic(String text) {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  TextDirection getTextDirection(String text) {
    if (containsArabic(text)) {
      return TextDirection.rtl;
    }
    return TextDirection.ltr;
  }
}
