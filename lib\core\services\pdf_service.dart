import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:invoice/features/invoice/domain/entities/invoice.dart';

class PdfService {
  static final PdfService _instance = PdfService._internal();
  factory PdfService() => _instance;
  PdfService._internal();

  static PdfService get instance => _instance;

  Future<File?> generateInvoicePdf(Invoice invoice) async {
    try {
      print('🔄 Starting PDF generation for invoice: ${invoice.invoiceNumber}');

      // Request storage permission
      final hasPermission = await _requestStoragePermission();
      if (!hasPermission) {
        print('❌ Storage permission denied');
        return null;
      }

      // Create PDF document
      final pdf = pw.Document();

      // Load fonts for Arabic support
      final arabicFont = await _loadArabicFont();
      final regularFont = await _loadRegularFont();

      // Generate PDF based on template
      await _generatePdfContent(pdf, invoice, arabicFont, regularFont);

      // Save PDF to device
      final file = await _savePdfToDevice(pdf, invoice);

      print('✅ PDF generated successfully: ${file?.path}');
      return file;
    } catch (e) {
      print('❌ Error generating PDF: $e');
      return null;
    }
  }

  Future<bool> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      if (status.isDenied) {
        final manageStatus = await Permission.manageExternalStorage.request();
        return manageStatus.isGranted;
      }
      return status.isGranted;
    }
    return true; // iOS doesn't need explicit storage permission for app documents
  }

  Future<pw.Font> _loadArabicFont() async {
    try {
      // Try to load Arabic font from assets
      final fontData =
          await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
      return pw.Font.ttf(fontData);
    } catch (e) {
      print('⚠️ Arabic font not found, using default font');
      // Fallback to default font
      return pw.Font.helvetica();
    }
  }

  Future<pw.Font> _loadRegularFont() async {
    return pw.Font.helvetica();
  }

  Future<void> _generatePdfContent(pw.Document pdf, Invoice invoice,
      pw.Font arabicFont, pw.Font regularFont) async {
    switch (invoice.template) {
      case 'Modern':
        await _generateModernTemplate(pdf, invoice, arabicFont, regularFont);
        break;
      case 'Classic':
        await _generateClassicTemplate(pdf, invoice, arabicFont, regularFont);
        break;
      case 'Minimal':
        await _generateMinimalTemplate(pdf, invoice, arabicFont, regularFont);
        break;
      case 'Creative':
        await _generateCreativeTemplate(pdf, invoice, arabicFont, regularFont);
        break;
      case 'Corporate':
        await _generateCorporateTemplate(pdf, invoice, arabicFont, regularFont);
        break;
      case 'Service':
        await _generateServiceTemplate(pdf, invoice, arabicFont, regularFont);
        break;
      default:
        await _generateModernTemplate(pdf, invoice, arabicFont, regularFont);
    }
  }

  Future<void> _generateModernTemplate(pw.Document pdf, Invoice invoice,
      pw.Font arabicFont, pw.Font regularFont) async {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Header
              _buildModernHeader(invoice, regularFont),
              pw.SizedBox(height: 30),

              // Business and Client Info
              _buildBusinessClientInfo(invoice, arabicFont, regularFont),
              pw.SizedBox(height: 30),

              // Items Table
              _buildItemsTable(invoice, arabicFont, regularFont),
              pw.SizedBox(height: 20),

              // Totals
              _buildTotalsSection(invoice, arabicFont, regularFont),
              pw.SizedBox(height: 30),

              // Footer
              _buildFooter(invoice, arabicFont, regularFont),
            ],
          );
        },
      ),
    );
  }

  pw.Widget _buildModernHeader(Invoice invoice, pw.Font font) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                invoice.invoiceNumber,
                style: pw.TextStyle(
                    font: font, fontSize: 14, color: PdfColors.grey600),
              ),
              pw.Text(
                'INVOICE',
                style: pw.TextStyle(
                  font: font,
                  fontSize: 32,
                  fontWeight: pw.FontWeight.bold,
                  letterSpacing: 2,
                ),
              ),
              pw.SizedBox(width: 60),
            ],
          ),
          pw.SizedBox(height: 20),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Invoice #Bill To',
                    style: pw.TextStyle(
                        font: font,
                        fontSize: 12,
                        fontWeight: pw.FontWeight.bold),
                  ),
                  pw.SizedBox(height: 4),
                  pw.Text(
                    'Creation Date',
                    style: pw.TextStyle(
                        font: font, fontSize: 11, color: PdfColors.grey600),
                  ),
                  pw.SizedBox(height: 2),
                  pw.Text(
                    'Due Date',
                    style: pw.TextStyle(
                        font: font, fontSize: 11, color: PdfColors.grey600),
                  ),
                ],
              ),
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  pw.Text(
                    invoice.clientName,
                    style: pw.TextStyle(
                        font: font,
                        fontSize: 12,
                        fontWeight: pw.FontWeight.bold),
                  ),
                  pw.SizedBox(height: 4),
                  pw.Text(
                    _formatDate(invoice.creationDate),
                    style: pw.TextStyle(font: font, fontSize: 11),
                  ),
                  pw.SizedBox(height: 2),
                  pw.Text(
                    _formatDate(invoice.dueDate),
                    style: pw.TextStyle(font: font, fontSize: 11),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  pw.Widget _buildBusinessClientInfo(
      Invoice invoice, pw.Font arabicFont, pw.Font regularFont) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          child: pw.Container(
            padding: const pw.EdgeInsets.all(16),
            decoration: pw.BoxDecoration(
              color: PdfColors.blue50,
              border: pw.Border.all(color: PdfColors.blue200),
              borderRadius: pw.BorderRadius.circular(8),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'From:',
                  style: pw.TextStyle(
                    font: regularFont,
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.blue700,
                  ),
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  invoice.businessName,
                  style: pw.TextStyle(
                      font: regularFont,
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold),
                ),
                pw.SizedBox(height: 4),
                pw.Text(invoice.businessEmail,
                    style: pw.TextStyle(font: regularFont, fontSize: 14)),
                pw.Text(invoice.businessPhone,
                    style: pw.TextStyle(font: regularFont, fontSize: 14)),
                pw.Text(invoice.businessAddress,
                    style: pw.TextStyle(font: regularFont, fontSize: 14)),
              ],
            ),
          ),
        ),
        pw.SizedBox(width: 20),
        pw.Expanded(
          child: pw.Container(
            padding: const pw.EdgeInsets.all(16),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey50,
              border: pw.Border.all(color: PdfColors.grey300),
              borderRadius: pw.BorderRadius.circular(8),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'To:',
                  style: pw.TextStyle(
                    font: regularFont,
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.grey600,
                  ),
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  invoice.clientName,
                  style: pw.TextStyle(
                      font: regularFont,
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold),
                ),
                pw.SizedBox(height: 4),
                if (invoice.clientCompany != null) ...[
                  pw.Text(invoice.clientCompany!,
                      style: pw.TextStyle(font: regularFont, fontSize: 14)),
                ],
                pw.Text(invoice.clientEmail,
                    style: pw.TextStyle(font: regularFont, fontSize: 14)),
                pw.Text(invoice.clientPhone,
                    style: pw.TextStyle(font: regularFont, fontSize: 14)),
                pw.Text(invoice.clientAddress,
                    style: pw.TextStyle(font: regularFont, fontSize: 14)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  pw.Widget _buildItemsTable(
      Invoice invoice, pw.Font arabicFont, pw.Font regularFont) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        children: [
          // Table header
          pw.Container(
            padding:
                const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const pw.BoxDecoration(
              color: PdfColor.fromInt(0xFF4A5568),
              borderRadius: pw.BorderRadius.only(
                topLeft: pw.Radius.circular(8),
                topRight: pw.Radius.circular(8),
              ),
            ),
            child: pw.Row(
              children: [
                pw.Expanded(
                  flex: 2,
                  child: pw.Text(
                    'Amount',
                    style: pw.TextStyle(
                      font: regularFont,
                      fontSize: 13,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.white,
                    ),
                  ),
                ),
                pw.Expanded(
                  flex: 2,
                  child: pw.Text(
                    'Price',
                    style: pw.TextStyle(
                      font: regularFont,
                      fontSize: 13,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.white,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
                pw.Expanded(
                  flex: 1,
                  child: pw.Text(
                    'QTY',
                    style: pw.TextStyle(
                      font: regularFont,
                      fontSize: 13,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.white,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
                pw.Expanded(
                  flex: 3,
                  child: pw.Text(
                    'Description',
                    style: pw.TextStyle(
                      font: regularFont,
                      fontSize: 13,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.white,
                    ),
                    textAlign: pw.TextAlign.right,
                  ),
                ),
              ],
            ),
          ),
          // Table rows
          ...invoice.items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return pw.Container(
              padding:
                  const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: pw.BoxDecoration(
                color: index % 2 == 0
                    ? PdfColors.white
                    : const PdfColor.fromInt(0xFFF8F9FA),
                border: pw.Border(
                  bottom: pw.BorderSide(color: PdfColors.grey200, width: 0.5),
                ),
              ),
              child: pw.Row(
                children: [
                  pw.Expanded(
                    flex: 2,
                    child: pw.Text(
                      '${item.amount.toStringAsFixed(2)}',
                      style: pw.TextStyle(
                        font: regularFont,
                        fontSize: 13,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ),
                  pw.Expanded(
                    flex: 2,
                    child: pw.Text(
                      '${item.unitPrice.toStringAsFixed(2)}',
                      style: pw.TextStyle(font: regularFont, fontSize: 13),
                      textAlign: pw.TextAlign.center,
                    ),
                  ),
                  pw.Expanded(
                    flex: 1,
                    child: pw.Text(
                      '${item.quantity}',
                      style: pw.TextStyle(font: regularFont, fontSize: 13),
                      textAlign: pw.TextAlign.center,
                    ),
                  ),
                  pw.Expanded(
                    flex: 3,
                    child: pw.Text(
                      item.name,
                      style: pw.TextStyle(
                        font: regularFont,
                        fontSize: 13,
                        fontWeight: pw.FontWeight.normal,
                      ),
                      textAlign: pw.TextAlign.right,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          // Total row
          pw.Container(
            padding:
                const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const pw.BoxDecoration(
              color: PdfColor.fromInt(0xFF4A5568),
              borderRadius: pw.BorderRadius.only(
                bottomLeft: pw.Radius.circular(8),
                bottomRight: pw.Radius.circular(8),
              ),
            ),
            child: pw.Row(
              children: [
                pw.Expanded(
                  flex: 2,
                  child: pw.Text(
                    'Total',
                    style: pw.TextStyle(
                      font: regularFont,
                      fontSize: 14,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.white,
                    ),
                  ),
                ),
                pw.Expanded(flex: 6, child: pw.SizedBox()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  pw.Widget _buildTotalsSection(
      Invoice invoice, pw.Font arabicFont, pw.Font regularFont) {
    return pw.Align(
      alignment: pw.Alignment.centerRight,
      child: pw.Container(
        width: 250,
        padding: const pw.EdgeInsets.all(16),
        decoration: pw.BoxDecoration(
          color: PdfColors.grey50,
          border: pw.Border.all(color: PdfColors.grey300),
          borderRadius: pw.BorderRadius.circular(8),
        ),
        child: pw.Column(
          children: [
            _buildTotalRow('Subtotal:', invoice.subtotal, regularFont),
            if (invoice.discount > 0)
              _buildTotalRow('Discount:', -invoice.discount, regularFont),
            if (invoice.tax > 0)
              _buildTotalRow('Tax:', invoice.tax, regularFont),
            if (invoice.shipping > 0)
              _buildTotalRow('Shipping:', invoice.shipping, regularFont),
            pw.Divider(thickness: 2),
            pw.Container(
              padding: const pw.EdgeInsets.all(8),
              decoration: pw.BoxDecoration(
                color: const PdfColor.fromInt(0xFF4285F4),
                borderRadius: pw.BorderRadius.circular(4),
              ),
              child: _buildTotalRow(
                'Total:',
                invoice.total,
                regularFont,
                isTotal: true,
                textColor: PdfColors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  pw.Widget _buildTotalRow(String label, double amount, pw.Font font,
      {bool isTotal = false, PdfColor? textColor}) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 4),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              font: font,
              fontSize: isTotal ? 18 : 14,
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
              color: textColor,
            ),
          ),
          pw.Text(
            '${amount.toStringAsFixed(2)} SAR',
            style: pw.TextStyle(
              font: font,
              fontSize: isTotal ? 18 : 14,
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  pw.Widget _buildFooter(
      Invoice invoice, pw.Font arabicFont, pw.Font regularFont) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        if (invoice.terms != null && invoice.terms!.isNotEmpty) ...[
          pw.Text(
            'Terms & Conditions:',
            style: pw.TextStyle(
              font: regularFont,
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            invoice.terms!,
            style: pw.TextStyle(font: regularFont, fontSize: 12),
          ),
          pw.SizedBox(height: 16),
        ],
        if (invoice.notes != null && invoice.notes!.isNotEmpty) ...[
          pw.Text(
            'Notes:',
            style: pw.TextStyle(
              font: regularFont,
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            invoice.notes!,
            style: pw.TextStyle(font: regularFont, fontSize: 12),
          ),
        ],
      ],
    );
  }

  // Other template methods (using Modern template as base for now)
  Future<void> _generateClassicTemplate(pw.Document pdf, Invoice invoice,
      pw.Font arabicFont, pw.Font regularFont) async {
    await _generateModernTemplate(pdf, invoice, arabicFont, regularFont);
  }

  Future<void> _generateMinimalTemplate(pw.Document pdf, Invoice invoice,
      pw.Font arabicFont, pw.Font regularFont) async {
    await _generateModernTemplate(pdf, invoice, arabicFont, regularFont);
  }

  Future<void> _generateCreativeTemplate(pw.Document pdf, Invoice invoice,
      pw.Font arabicFont, pw.Font regularFont) async {
    await _generateModernTemplate(pdf, invoice, arabicFont, regularFont);
  }

  Future<void> _generateCorporateTemplate(pw.Document pdf, Invoice invoice,
      pw.Font arabicFont, pw.Font regularFont) async {
    await _generateModernTemplate(pdf, invoice, arabicFont, regularFont);
  }

  Future<void> _generateServiceTemplate(pw.Document pdf, Invoice invoice,
      pw.Font arabicFont, pw.Font regularFont) async {
    await _generateModernTemplate(pdf, invoice, arabicFont, regularFont);
  }

  Future<File?> _savePdfToDevice(pw.Document pdf, Invoice invoice) async {
    try {
      final bytes = await pdf.save();

      // Get the appropriate directory
      Directory directory;
      if (Platform.isAndroid) {
        directory = Directory('/storage/emulated/0/Download');
        if (!await directory.exists()) {
          directory = await getExternalStorageDirectory() ??
              await getApplicationDocumentsDirectory();
        }
      } else {
        directory = await getApplicationDocumentsDirectory();
      }

      // Create filename
      final fileName = _generateFileName(invoice);
      final file = File('${directory.path}/$fileName');

      // Write PDF to file
      await file.writeAsBytes(bytes);

      print('✅ PDF saved to: ${file.path}');
      return file;
    } catch (e) {
      print('❌ Error saving PDF: $e');
      return null;
    }
  }

  String _generateFileName(Invoice invoice) {
    // Clean client name for filename
    final cleanClientName = invoice.clientName
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'\s+'), '_');

    return 'Invoice_${invoice.invoiceNumber}_$cleanClientName.pdf';
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
