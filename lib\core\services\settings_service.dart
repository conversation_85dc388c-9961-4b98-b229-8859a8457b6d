import 'package:invoice/core/database/database_helper.dart';
import 'package:sqflite/sqflite.dart';

class SettingsService {
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper.instance;

  // Cache for settings to avoid frequent database calls
  Map<String, String>? _settingsCache;

  // Initialize settings and load cache
  Future<void> initialize() async {
    await _loadSettingsCache();
  }

  // Load all settings into cache
  Future<void> _loadSettingsCache() async {
    try {
      _settingsCache = await _dbHelper.getAllSettings();
    } catch (e) {
      print('Error loading settings cache: $e');
      _settingsCache = {};
    }
  }

  // Get setting value with fallback to default
  Future<String> getSetting(String key, {String defaultValue = ''}) async {
    if (_settingsCache == null) {
      await _loadSettingsCache();
    }
    return _settingsCache?[key] ?? defaultValue;
  }

  // Set setting value and update cache
  Future<void> setSetting(String key, String value, String category) async {
    await _dbHelper.setSetting(key, value, category);
    _settingsCache ??= {};
    _settingsCache![key] = value;
  }

  // Get all settings by category
  Future<Map<String, String>> getSettingsByCategory(String category) async {
    return await _dbHelper.getSettingsByCategory(category);
  }

  // Clear cache (useful for testing or reset)
  void clearCache() {
    _settingsCache = null;
  }

  // General Settings
  Future<String> getDefaultCurrency() async {
    return await getSetting('default_currency', defaultValue: 'SAR');
  }

  Future<String> getNumberFormat() async {
    return await getSetting('number_format', defaultValue: '1,000,000.00');
  }

  Future<String> getDateFormat() async {
    return await getSetting('date_format', defaultValue: 'yyyy/MM/dd');
  }

  Future<String> getLanguage() async {
    return await getSetting('language', defaultValue: 'Arabic');
  }

  // Business Settings
  Future<double> getTaxRate() async {
    final taxRateStr = await getSetting('tax_rate', defaultValue: '15');
    return double.tryParse(taxRateStr) ?? 15.0;
  }

  Future<String> getTaxType() async {
    return await getSetting('tax_type', defaultValue: 'percentage');
  }

  Future<String> getPaymentMethod() async {
    return await getSetting('payment_method', defaultValue: 'Bank Transfer');
  }

  Future<String> getTermsConditions() async {
    return await getSetting('terms_conditions',
        defaultValue: 'Payment is due within 30 days');
  }

  Future<String> getSignature() async {
    return await getSetting('signature', defaultValue: '');
  }

  // Invoice Settings
  Future<String> getDueTerms() async {
    return await getSetting('due_terms', defaultValue: '7 days');
  }

  Future<bool> getShowPaidOnInvoice() async {
    final showPaidStr =
        await getSetting('show_paid_on_invoice', defaultValue: 'true');
    return showPaidStr.toLowerCase() == 'true';
  }

  Future<String> getDefaultTemplate() async {
    return await getSetting('default_template', defaultValue: 'Modern');
  }

  // Business Information (from business_info table)
  Future<Map<String, String>> getBusinessInfo() async {
    try {
      final db = await _dbHelper.database;
      final result = await db.query('business_info', limit: 1);

      if (result.isNotEmpty) {
        final business = result.first;
        return {
          'name': business['name'] as String? ?? '',
          'email': business['email'] as String? ?? '',
          'phone': business['phone'] as String? ?? '',
          'address': business['address'] as String? ?? '',
          'website': business['website'] as String? ?? '',
          'tax_id': business['tax_id'] as String? ?? '',
          'company': business['company'] as String? ?? '',
        };
      }
    } catch (e) {
      print('Error loading business info: $e');
    }

    return {
      'name': '',
      'email': '',
      'phone': '',
      'address': '',
      'website': '',
      'tax_id': '',
      'company': '',
    };
  }

  // Set business information
  Future<void> setBusinessInfo(Map<String, String> businessInfo) async {
    try {
      final db = await _dbHelper.database;
      final now = DateTime.now().toIso8601String();

      await db.insert(
        'business_info',
        {
          'id': 1, // INTEGER value
          'name': businessInfo['name'] ?? '',
          'email': businessInfo['email'] ?? '',
          'phone': businessInfo['phone'] ?? '',
          'address': businessInfo['address'] ?? '',
          'website': businessInfo['website'] ?? '',
          'tax_id': businessInfo['tax_id'] ?? '',
          'company': businessInfo['company'] ?? '',
          'logo_path': '', // Add missing logo_path field
          'created_at': now,
          'updated_at': now,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      print('Error saving business info: $e');
      rethrow;
    }
  }

  // Format number according to settings
  String formatNumber(double number) {
    // This is a simplified implementation
    // You can enhance this based on the number format setting
    return number.toStringAsFixed(2);
  }

  // Format currency according to settings
  Future<String> formatCurrency(double amount) async {
    final currency = await getDefaultCurrency();
    final formattedAmount = formatNumber(amount);
    return '$formattedAmount $currency';
  }

  // Parse due terms to get number of days
  int parseDueTerms(String dueTerms) {
    if (dueTerms.contains('days')) {
      final match = RegExp(r'(\d+)').firstMatch(dueTerms);
      if (match != null) {
        return int.tryParse(match.group(1)!) ?? 7;
      }
    }

    // Handle special cases
    switch (dueTerms.toLowerCase()) {
      case 'due on receipt':
        return 0;
      case 'net 10':
        return 10;
      case 'net 15':
        return 15;
      case 'net 30':
        return 30;
      default:
        return 7;
    }
  }

  // Calculate due date based on creation date and due terms
  Future<DateTime> calculateDueDate(DateTime creationDate) async {
    final dueTerms = await getDueTerms();
    final days = parseDueTerms(dueTerms);
    return creationDate.add(Duration(days: days));
  }
}
