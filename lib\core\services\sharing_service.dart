import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:invoice/features/invoice/domain/entities/invoice.dart';
import 'package:invoice/core/services/pdf_service.dart';

class SharingService {
  static final SharingService _instance = SharingService._internal();
  factory SharingService() => _instance;
  SharingService._internal();

  static SharingService get instance => _instance;

  Future<bool> shareInvoicePdf(Invoice invoice) async {
    try {
      print('🔄 Starting PDF sharing for invoice: ${invoice.invoiceNumber}');

      // Generate PDF
      final pdfFile = await PdfService.instance.generateInvoicePdf(invoice);
      if (pdfFile == null) {
        print('❌ Failed to generate PDF for sharing');
        return false;
      }

      // Share the PDF file
      final result = await Share.shareXFiles(
        [XFile(pdfFile.path)],
        text: 'Invoice ${invoice.invoiceNumber} from ${invoice.businessName}',
        subject: 'Invoice ${invoice.invoiceNumber}',
      );

      print('✅ PDF sharing completed: ${result.status}');
      return result.status == ShareResultStatus.success;
    } catch (e) {
      print('❌ Error sharing PDF: $e');
      return false;
    }
  }

  Future<bool> shareInvoiceImage(
      GlobalKey repaintBoundaryKey, Invoice invoice) async {
    try {
      print('🔄 Starting image sharing for invoice: ${invoice.invoiceNumber}');

      // Capture widget as image
      final imageFile =
          await _captureWidgetAsImage(repaintBoundaryKey, invoice);
      if (imageFile == null) {
        print('❌ Failed to capture invoice as image');
        return false;
      }

      // Share the image file
      final result = await Share.shareXFiles(
        [XFile(imageFile.path)],
        text: 'Invoice ${invoice.invoiceNumber} from ${invoice.businessName}',
        subject: 'Invoice ${invoice.invoiceNumber}',
      );

      print('✅ Image sharing completed: ${result.status}');
      return result.status == ShareResultStatus.success;
    } catch (e) {
      print('❌ Error sharing image: $e');
      return false;
    }
  }

  Future<File?> _captureWidgetAsImage(
      GlobalKey repaintBoundaryKey, Invoice invoice) async {
    try {
      // Get the RenderRepaintBoundary
      final RenderRepaintBoundary boundary = repaintBoundaryKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;

      // Capture the image
      final image = await boundary.toImage(pixelRatio: 2.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final bytes = byteData!.buffer.asUint8List();

      // Save to temporary file
      final directory = await getTemporaryDirectory();
      final fileName = _generateImageFileName(invoice);
      final file = File('${directory.path}/$fileName');

      await file.writeAsBytes(bytes);

      print('✅ Invoice image captured: ${file.path}');
      return file;
    } catch (e) {
      print('❌ Error capturing invoice image: $e');
      return null;
    }
  }

  Future<void> showSharingOptions(BuildContext context, Invoice invoice,
      {GlobalKey? repaintBoundaryKey}) async {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            // Title
            Text(
              'Share Invoice',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 20),

            // Share options
            _buildShareOption(
              context,
              icon: Icons.picture_as_pdf,
              title: 'Share as PDF',
              subtitle: 'Share invoice as PDF document',
              color: Colors.red,
              onTap: () async {
                Navigator.pop(context);
                await _showLoadingAndShare(
                  context,
                  () => shareInvoicePdf(invoice),
                  'Generating PDF...',
                );
              },
            ),

            if (repaintBoundaryKey != null) ...[
              const SizedBox(height: 12),
              _buildShareOption(
                context,
                icon: Icons.image,
                title: 'Share as Image',
                subtitle: 'Share invoice as PNG image',
                color: Colors.blue,
                onTap: () async {
                  Navigator.pop(context);
                  await _showLoadingAndShare(
                    context,
                    () => shareInvoiceImage(repaintBoundaryKey, invoice),
                    'Capturing image...',
                  );
                },
              ),
            ],

            const SizedBox(height: 12),
            _buildShareOption(
              context,
              icon: Icons.link,
              title: 'Share Invoice Details',
              subtitle: 'Share invoice information as text',
              color: Colors.green,
              onTap: () async {
                Navigator.pop(context);
                await _shareInvoiceText(invoice);
              },
            ),

            const SizedBox(height: 20),

            // Cancel button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () => Navigator.pop(context),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('Cancel'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShareOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  Future<void> _showLoadingAndShare(
    BuildContext context,
    Future<bool> Function() shareFunction,
    String loadingMessage,
  ) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(loadingMessage),
          ],
        ),
      ),
    );

    try {
      final success = await shareFunction();

      // Close loading dialog
      if (context.mounted) Navigator.pop(context);

      // Show result message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? 'Invoice shared successfully!'
                : 'Failed to share invoice'),
            backgroundColor: success ? Colors.green : Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (context.mounted) Navigator.pop(context);

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing invoice: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _shareInvoiceText(Invoice invoice) async {
    final text = _generateInvoiceText(invoice);
    await Share.share(
      text,
      subject: 'Invoice ${invoice.invoiceNumber}',
    );
  }

  String _generateInvoiceText(Invoice invoice) {
    final buffer = StringBuffer();

    buffer.writeln('📄 INVOICE ${invoice.invoiceNumber}');
    buffer.writeln('');
    buffer.writeln('From: ${invoice.businessName}');
    buffer.writeln('Email: ${invoice.businessEmail}');
    buffer.writeln('Phone: ${invoice.businessPhone}');
    buffer.writeln('');
    buffer.writeln('To: ${invoice.clientName}');
    buffer.writeln('Email: ${invoice.clientEmail}');
    buffer.writeln('Phone: ${invoice.clientPhone}');
    buffer.writeln('');
    buffer.writeln('Creation Date: ${_formatDate(invoice.creationDate)}');
    buffer.writeln('Due Date: ${_formatDate(invoice.dueDate)}');
    buffer.writeln('');
    buffer.writeln('Items:');
    for (final item in invoice.items) {
      buffer.writeln(
          '• ${item.name} - Qty: ${item.quantity} - Price: ${item.unitPrice.toStringAsFixed(2)} SAR - Total: ${item.amount.toStringAsFixed(2)} SAR');
    }
    buffer.writeln('');
    buffer.writeln('Total Amount: ${invoice.total.toStringAsFixed(2)} SAR');
    buffer.writeln('Status: ${invoice.status.toUpperCase()}');

    return buffer.toString();
  }

  String _generateImageFileName(Invoice invoice) {
    final cleanClientName = invoice.clientName
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'\s+'), '_');

    return 'Invoice_${invoice.invoiceNumber}_$cleanClientName.png';
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
