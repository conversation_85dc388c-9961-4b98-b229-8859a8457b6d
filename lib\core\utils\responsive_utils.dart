import 'package:flutter/material.dart';

class ResponsiveUtils {
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }

  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < 360;
  }

  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(12);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(16);
    } else {
      return const EdgeInsets.all(24);
    }
  }

  static double getResponsiveFontSize(
      BuildContext context, double baseFontSize) {
    final width = getScreenWidth(context);
    if (width < 360) {
      return baseFontSize * 0.9;
    } else if (width > 600) {
      return baseFontSize * 1.1;
    }
    return baseFontSize;
  }

  static double getResponsiveSpacing(BuildContext context, double baseSpacing) {
    if (isMobile(context)) {
      return baseSpacing * 0.8;
    } else if (isTablet(context)) {
      return baseSpacing;
    } else {
      return baseSpacing * 1.2;
    }
  }

  static int getResponsiveColumns(BuildContext context) {
    final width = getScreenWidth(context);
    if (width < 600) {
      return 1;
    } else if (width < 900) {
      return 2;
    } else {
      return 3;
    }
  }

  static double getResponsiveCardWidth(BuildContext context) {
    final width = getScreenWidth(context);
    if (isMobile(context)) {
      return width - 32; // Full width minus padding
    } else if (isTablet(context)) {
      return (width - 48) / 2; // Two columns
    } else {
      return (width - 64) / 3; // Three columns
    }
  }

  static bool shouldUseCompactLayout(BuildContext context) {
    return isMobile(context) || isLandscape(context);
  }

  static double getMaxContentWidth(BuildContext context) {
    final width = getScreenWidth(context);
    if (width > 1200) {
      return 1200; // Max content width for very large screens
    }
    return width;
  }

  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  static double getAppBarHeight(BuildContext context) {
    return kToolbarHeight + getSafeAreaPadding(context).top;
  }

  static double getBottomNavigationHeight(BuildContext context) {
    return kBottomNavigationBarHeight + getSafeAreaPadding(context).bottom;
  }

  static double getAvailableHeight(BuildContext context) {
    return getScreenHeight(context) -
        getAppBarHeight(context) -
        getBottomNavigationHeight(context);
  }

  // Text scaling utilities
  static TextStyle getResponsiveTextStyle(
      BuildContext context, TextStyle baseStyle) {
    final textScaler = MediaQuery.of(context).textScaler;
    final scaleFactor = textScaler.scale(1.0).clamp(0.8, 1.3);

    return baseStyle.copyWith(
      fontSize: (baseStyle.fontSize ?? 14) * scaleFactor,
    );
  }

  // Layout direction utilities for RTL support
  static bool isRTL(BuildContext context) {
    return Directionality.of(context) == TextDirection.rtl;
  }

  static EdgeInsetsDirectional getDirectionalPadding(
    BuildContext context, {
    double start = 0,
    double top = 0,
    double end = 0,
    double bottom = 0,
  }) {
    return EdgeInsetsDirectional.fromSTEB(start, top, end, bottom);
  }

  // Responsive grid utilities
  static int getGridCrossAxisCount(
    BuildContext context, {
    double childAspectRatio = 1.0,
    double maxCrossAxisExtent = 200,
  }) {
    final width = getScreenWidth(context);
    return (width / maxCrossAxisExtent).floor().clamp(1, 4);
  }

  // Responsive dialog sizing
  static Size getDialogSize(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    if (isMobile(context)) {
      return Size(
        screenSize.width * 0.9,
        screenSize.height * 0.8,
      );
    } else {
      return Size(
        (screenSize.width * 0.7).clamp(400, 800),
        (screenSize.height * 0.8).clamp(300, 600),
      );
    }
  }

  // Responsive button sizing
  static Size getButtonSize(BuildContext context, {bool isCompact = false}) {
    if (isCompact || isMobile(context)) {
      return const Size(120, 40);
    } else {
      return const Size(140, 48);
    }
  }

  // Responsive icon sizing
  static double getIconSize(BuildContext context, {double baseSize = 24}) {
    if (isSmallScreen(context)) {
      return baseSize * 0.9;
    } else if (isTablet(context)) {
      return baseSize * 1.1;
    }
    return baseSize;
  }

  // Responsive spacing between widgets
  static Widget getResponsiveSpacingWidget(
    BuildContext context, {
    double baseSpacing = 16,
    Axis direction = Axis.vertical,
  }) {
    final spacing = getResponsiveSpacing(context, baseSpacing);
    if (direction == Axis.vertical) {
      return SizedBox(height: spacing);
    } else {
      return SizedBox(width: spacing);
    }
  }

  // Responsive container constraints
  static BoxConstraints getResponsiveConstraints(
    BuildContext context, {
    double? minWidth,
    double? maxWidth,
    double? minHeight,
    double? maxHeight,
  }) {
    final screenWidth = getScreenWidth(context);
    final screenHeight = getScreenHeight(context);

    return BoxConstraints(
      minWidth: minWidth ?? 0,
      maxWidth: maxWidth ?? screenWidth,
      minHeight: minHeight ?? 0,
      maxHeight: maxHeight ?? screenHeight,
    );
  }

  // Responsive flex values for layouts
  static int getResponsiveFlex(BuildContext context, int baseFlex) {
    if (isSmallScreen(context)) {
      return (baseFlex * 0.8).round().clamp(1, 10);
    }
    return baseFlex;
  }

  // Check if content should wrap to next line
  static bool shouldWrapContent(
      BuildContext context, int itemCount, double itemWidth) {
    final availableWidth =
        getScreenWidth(context) - getResponsivePadding(context).horizontal;
    final totalItemWidth = itemCount * itemWidth;
    return totalItemWidth > availableWidth;
  }
}
