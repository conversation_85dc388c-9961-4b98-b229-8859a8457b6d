import 'package:flutter/material.dart';
import 'package:invoice/core/database/database_helper.dart';

class DatabaseTestScreen extends StatefulWidget {
  const DatabaseTestScreen({super.key});

  @override
  State<DatabaseTestScreen> createState() => _DatabaseTestScreenState();
}

class _DatabaseTestScreenState extends State<DatabaseTestScreen> {
  List<Map<String, dynamic>> invoices = [];
  List<Map<String, dynamic>> clients = [];
  List<Map<String, dynamic>> items = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  Future<void> _loadAllData() async {
    try {
      final db = await DatabaseHelper.instance.database;
      
      final invoicesResult = await db.query('invoices');
      final clientsResult = await db.query('clients');
      final itemsResult = await db.query('items');
      
      setState(() {
        invoices = invoicesResult;
        clients = clientsResult;
        items = itemsResult;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحميل البيانات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _clearAllData() async {
    try {
      final db = await DatabaseHelper.instance.database;
      
      await db.delete('invoice_items');
      await db.delete('invoices');
      await db.delete('clients');
      await db.delete('items');
      
      _loadAllData();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم مسح جميع البيانات'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في مسح البيانات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار قاعدة البيانات'),
        backgroundColor: const Color(0xFF4285F4),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAllData,
          ),
          IconButton(
            icon: const Icon(Icons.delete_forever),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('تأكيد المسح'),
                  content: const Text('هل تريد مسح جميع البيانات؟'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _clearAllData();
                      },
                      style: TextButton.styleFrom(foregroundColor: Colors.red),
                      child: const Text('مسح'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSection('الفواتير', invoices, Colors.blue),
                  const SizedBox(height: 20),
                  _buildSection('العملاء', clients, Colors.green),
                  const SizedBox(height: 20),
                  _buildSection('المنتجات', items, Colors.orange),
                ],
              ),
            ),
    );
  }

  Widget _buildSection(String title, List<Map<String, dynamic>> data, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getIconForSection(title),
                  color: color,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  '$title (${data.length})',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (data.isEmpty)
              const Text(
                'لا توجد بيانات',
                style: TextStyle(
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              )
            else
              ...data.take(3).map((item) => _buildDataItem(item, title)),
            if (data.length > 3)
              Text(
                '... و ${data.length - 3} عنصر آخر',
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataItem(Map<String, dynamic> item, String section) {
    String displayText = '';
    
    switch (section) {
      case 'الفواتير':
        displayText = 'رقم الفاتورة: ${item['invoice_number']} - العميل: ${item['client_name']} - المجموع: ${item['total']}';
        break;
      case 'العملاء':
        displayText = 'الاسم: ${item['name']} - الإيميل: ${item['email']}';
        break;
      case 'المنتجات':
        displayText = 'الاسم: ${item['name']} - السعر: ${item['unit_price']}';
        break;
    }
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          displayText,
          style: const TextStyle(fontSize: 12),
        ),
      ),
    );
  }

  IconData _getIconForSection(String section) {
    switch (section) {
      case 'الفواتير':
        return Icons.receipt_long;
      case 'العملاء':
        return Icons.people;
      case 'المنتجات':
        return Icons.inventory;
      default:
        return Icons.data_object;
    }
  }
}
