import 'package:invoice/features/invoice/domain/entities/invoice.dart';

class InvoiceModel {
  final String id;
  final String invoiceNumber;
  final DateTime creationDate;
  final DateTime dueDate;
  final String dueTerms;
  final String? poNumber;
  final String invoiceTitle;
  final String businessName;
  final String businessEmail;
  final String businessPhone;
  final String businessAddress;
  final String? businessWebsite;
  final String? businessTaxId;
  final String? businessCompany;
  final String clientName;
  final String clientEmail;
  final String clientPhone;
  final String clientAddress;
  final String? clientCompany;
  final String? clientTaxId;
  final List<InvoiceItemModel> items;
  final double subtotal;
  final double discount;
  final String discountType;
  final double tax;
  final String taxType;
  final double shipping;
  final double total;
  final String currency;
  final String language;
  final String template;
  final String status;
  final String? notes;
  final String? terms;
  final String? signature;
  final String? paymentMethod;
  final DateTime createdAt;
  final DateTime updatedAt;

  const InvoiceModel({
    required this.id,
    required this.invoiceNumber,
    required this.creationDate,
    required this.dueDate,
    this.dueTerms = '7 days',
    this.poNumber,
    this.invoiceTitle = 'INVOICE',
    required this.businessName,
    required this.businessEmail,
    required this.businessPhone,
    required this.businessAddress,
    this.businessWebsite,
    this.businessTaxId,
    this.businessCompany,
    required this.clientName,
    required this.clientEmail,
    required this.clientPhone,
    required this.clientAddress,
    this.clientCompany,
    this.clientTaxId,
    required this.items,
    this.subtotal = 0.0,
    this.discount = 0.0,
    this.discountType = 'percentage',
    this.tax = 0.0,
    this.taxType = 'percentage',
    this.shipping = 0.0,
    this.total = 0.0,
    this.currency = 'SAR',
    this.language = 'English',
    this.template = 'Modern',
    this.status = 'unpaid',
    this.notes,
    this.terms,
    this.signature,
    this.paymentMethod,
    required this.createdAt,
    required this.updatedAt,
  });

  Invoice toEntity() {
    return Invoice(
      id: id,
      invoiceNumber: invoiceNumber,
      creationDate: creationDate,
      dueDate: dueDate,
      dueTerms: dueTerms,
      poNumber: poNumber,
      invoiceTitle: invoiceTitle,
      businessName: businessName,
      businessEmail: businessEmail,
      businessPhone: businessPhone,
      businessAddress: businessAddress,
      businessWebsite: businessWebsite,
      businessTaxId: businessTaxId,
      businessCompany: businessCompany,
      clientName: clientName,
      clientEmail: clientEmail,
      clientPhone: clientPhone,
      clientAddress: clientAddress,
      clientCompany: clientCompany,
      clientTaxId: clientTaxId,
      items: items.map((item) => item.toEntity()).toList(),
      subtotal: subtotal,
      discount: discount,
      discountType: discountType,
      tax: tax,
      taxType: taxType,
      shipping: shipping,
      total: total,
      currency: currency,
      language: language,
      template: template,
      status: status,
      notes: notes,
      terms: terms,
      signature: signature,
      paymentMethod: paymentMethod,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  factory InvoiceModel.fromEntity(Invoice invoice) {
    return InvoiceModel(
      id: invoice.id,
      invoiceNumber: invoice.invoiceNumber,
      creationDate: invoice.creationDate,
      dueDate: invoice.dueDate,
      dueTerms: invoice.dueTerms,
      poNumber: invoice.poNumber,
      invoiceTitle: invoice.invoiceTitle,
      businessName: invoice.businessName,
      businessEmail: invoice.businessEmail,
      businessPhone: invoice.businessPhone,
      businessAddress: invoice.businessAddress,
      businessWebsite: invoice.businessWebsite,
      businessTaxId: invoice.businessTaxId,
      businessCompany: invoice.businessCompany,
      clientName: invoice.clientName,
      clientEmail: invoice.clientEmail,
      clientPhone: invoice.clientPhone,
      clientAddress: invoice.clientAddress,
      clientCompany: invoice.clientCompany,
      clientTaxId: invoice.clientTaxId,
      items: invoice.items
          .map((item) => InvoiceItemModel.fromEntity(item))
          .toList(),
      subtotal: invoice.subtotal,
      discount: invoice.discount,
      discountType: invoice.discountType,
      tax: invoice.tax,
      taxType: invoice.taxType,
      shipping: invoice.shipping,
      total: invoice.total,
      currency: invoice.currency,
      language: invoice.language,
      template: invoice.template,
      status: invoice.status,
      notes: invoice.notes,
      terms: invoice.terms,
      signature: invoice.signature,
      paymentMethod: invoice.paymentMethod,
      createdAt: invoice.createdAt,
      updatedAt: invoice.updatedAt,
    );
  }

  factory InvoiceModel.fromJson(Map<String, dynamic> json) {
    return InvoiceModel(
      id: json['id'] as String,
      invoiceNumber: json['invoice_number'] as String,
      creationDate: DateTime.parse(json['creation_date'] as String),
      dueDate: DateTime.parse(json['due_date'] as String),
      dueTerms: json['due_terms'] as String? ?? '7 days',
      poNumber: json['po_number'] as String?,
      invoiceTitle: json['invoice_title'] as String? ?? 'INVOICE',
      businessName: json['business_name'] as String,
      businessEmail: json['business_email'] as String,
      businessPhone: json['business_phone'] as String,
      businessAddress: json['business_address'] as String,
      businessWebsite: json['business_website'] as String?,
      businessTaxId: json['business_tax_id'] as String?,
      businessCompany: json['business_company'] as String?,
      clientName: json['client_name'] as String,
      clientEmail: json['client_email'] as String,
      clientPhone: json['client_phone'] as String,
      clientAddress: json['client_address'] as String,
      clientCompany: json['client_company'] as String?,
      clientTaxId: json['client_tax_id'] as String?,
      items: (json['items'] as List<dynamic>?)
              ?.map((item) =>
                  InvoiceItemModel.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      subtotal: (json['subtotal'] as num?)?.toDouble() ?? 0.0,
      discount: (json['discount'] as num?)?.toDouble() ?? 0.0,
      discountType: json['discount_type'] as String? ?? 'percentage',
      tax: (json['tax'] as num?)?.toDouble() ?? 0.0,
      taxType: json['tax_type'] as String? ?? 'percentage',
      shipping: (json['shipping'] as num?)?.toDouble() ?? 0.0,
      total: (json['total'] as num?)?.toDouble() ?? 0.0,
      currency: json['currency'] as String? ?? 'SAR',
      language: json['language'] as String? ?? 'English',
      template: json['template'] as String? ?? 'Modern',
      status: json['status'] as String? ?? 'unpaid',
      notes: json['notes'] as String?,
      terms: json['terms'] as String?,
      signature: json['signature'] as String?,
      paymentMethod: json['payment_method'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'creation_date': creationDate.toIso8601String(),
      'due_date': dueDate.toIso8601String(),
      'due_terms': dueTerms,
      'po_number': poNumber,
      'invoice_title': invoiceTitle,
      'business_name': businessName,
      'business_email': businessEmail,
      'business_phone': businessPhone,
      'business_address': businessAddress,
      'business_website': businessWebsite,
      'business_tax_id': businessTaxId,
      'business_company': businessCompany,
      'client_name': clientName,
      'client_email': clientEmail,
      'client_phone': clientPhone,
      'client_address': clientAddress,
      'client_company': clientCompany,
      'client_tax_id': clientTaxId,
      'items': items.map((item) => item.toJson()).toList(),
      'subtotal': subtotal,
      'discount': discount,
      'discount_type': discountType,
      'tax': tax,
      'tax_type': taxType,
      'shipping': shipping,
      'total': total,
      'currency': currency,
      'language': language,
      'template': template,
      'status': status,
      'notes': notes,
      'terms': terms,
      'signature': signature,
      'payment_method': paymentMethod,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class InvoiceItemModel {
  final String id;
  final String name;
  final String? description;
  final double quantity;
  final double unitPrice;
  final double amount;
  final String? notes;
  final DateTime createdAt;

  const InvoiceItemModel({
    required this.id,
    required this.name,
    this.description,
    this.quantity = 1.0,
    this.unitPrice = 0.0,
    this.amount = 0.0,
    this.notes,
    required this.createdAt,
  });

  InvoiceItem toEntity() {
    return InvoiceItem(
      id: id,
      name: name,
      description: description,
      quantity: quantity,
      unitPrice: unitPrice,
      amount: amount,
      notes: notes,
      createdAt: createdAt,
    );
  }

  factory InvoiceItemModel.fromEntity(InvoiceItem item) {
    return InvoiceItemModel(
      id: item.id,
      name: item.name,
      description: item.description,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      amount: item.amount,
      notes: item.notes,
      createdAt: item.createdAt,
    );
  }

  factory InvoiceItemModel.fromJson(Map<String, dynamic> json) {
    return InvoiceItemModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      quantity: (json['quantity'] as num).toDouble(),
      unitPrice: (json['unit_price'] as num).toDouble(),
      amount: (json['amount'] as num).toDouble(),
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'quantity': quantity,
      'unit_price': unitPrice,
      'amount': amount,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
