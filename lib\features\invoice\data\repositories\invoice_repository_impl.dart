import 'package:dartz/dartz.dart';
import 'package:sqflite/sqflite.dart';
import 'package:invoice/core/database/database_helper.dart';
import 'package:invoice/core/error/failures.dart';
import 'package:invoice/features/invoice/data/models/invoice_model.dart';
import 'package:invoice/features/invoice/domain/entities/invoice.dart';
import 'package:invoice/features/invoice/domain/repositories/invoice_repository.dart';
import 'package:uuid/uuid.dart';

class InvoiceRepositoryImpl implements InvoiceRepository {
  final DatabaseHelper _databaseHelper;
  final Uuid _uuid;

  InvoiceRepositoryImpl({
    required DatabaseHelper databaseHelper,
    required Uuid uuid,
  })  : _databaseHelper = databaseHelper,
        _uuid = uuid;

  @override
  Future<Either<Failure, String>> createInvoice(Invoice invoice) async {
    try {
      final db = await _databaseHelper.database;
      final invoiceModel = InvoiceModel.fromEntity(invoice);
      final id = _uuid.v4();

      await db.transaction((txn) async {
        // Insert invoice (without items)
        await txn.insert(
          'invoices',
          {
            'id': id,
            'invoice_number': invoiceModel.invoiceNumber,
            'creation_date': invoiceModel.creationDate.toIso8601String(),
            'due_date': invoiceModel.dueDate.toIso8601String(),
            'due_terms': invoiceModel.dueTerms,
            'po_number': invoiceModel.poNumber,
            'invoice_title': invoiceModel.invoiceTitle,
            'business_name': invoiceModel.businessName,
            'business_email': invoiceModel.businessEmail,
            'business_phone': invoiceModel.businessPhone,
            'business_address': invoiceModel.businessAddress,
            'business_website': invoiceModel.businessWebsite,
            'business_tax_id': invoiceModel.businessTaxId,
            'business_company': invoiceModel.businessCompany,
            'client_name': invoiceModel.clientName,
            'client_email': invoiceModel.clientEmail,
            'client_phone': invoiceModel.clientPhone,
            'client_address': invoiceModel.clientAddress,
            'client_company': invoiceModel.clientCompany,
            'client_tax_id': invoiceModel.clientTaxId,
            'subtotal': invoiceModel.subtotal,
            'discount': invoiceModel.discount,
            'discount_type': invoiceModel.discountType,
            'tax': invoiceModel.tax,
            'tax_type': invoiceModel.taxType,
            'shipping': invoiceModel.shipping,
            'total': invoiceModel.total,
            'currency': invoiceModel.currency,
            'language': invoiceModel.language,
            'template': invoiceModel.template,
            'status': invoiceModel.status,
            'notes': invoiceModel.notes,
            'terms': invoiceModel.terms,
            'signature': invoiceModel.signature,
            'payment_method': invoiceModel.paymentMethod,
            'created_at': invoiceModel.createdAt.toIso8601String(),
            'updated_at': invoiceModel.updatedAt.toIso8601String(),
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );

        // Insert invoice items
        for (var item in invoiceModel.items) {
          await txn.insert(
            'invoice_items',
            {...item.toJson(), 'invoice_id': id},
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
      });

      return Right(id);
    } catch (e) {
      return Left(DatabaseFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteInvoice(String id) async {
    try {
      final db = await _databaseHelper.database;
      final deletedCount = await db.delete(
        'invoices',
        where: 'id = ?',
        whereArgs: [id],
      );
      return Right(deletedCount > 0);
    } catch (e) {
      return Left(DatabaseFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Invoice>>> getAllInvoices() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> invoices = await db.query('invoices');
      final List<Invoice> result = [];

      for (var invoiceMap in invoices) {
        final List<Map<String, dynamic>> items = await db.query(
          'invoice_items',
          where: 'invoice_id = ?',
          whereArgs: [invoiceMap['id']],
        );

        invoiceMap['items'] = items;
        result.add(InvoiceModel.fromJson(invoiceMap).toEntity());
      }

      return Right(result);
    } catch (e) {
      return Left(DatabaseFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Invoice>> getInvoiceById(String id) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> invoices = await db.query(
        'invoices',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (invoices.isEmpty) {
        return const Left(DatabaseFailure(message: 'Invoice not found'));
      }

      final invoiceMap = invoices.first;
      final List<Map<String, dynamic>> items = await db.query(
        'invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [id],
      );

      invoiceMap['items'] = items;
      return Right(InvoiceModel.fromJson(invoiceMap).toEntity());
    } catch (e) {
      return Left(DatabaseFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Invoice>>> getInvoicesByStatus(
      String status) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> invoices = await db.query(
        'invoices',
        where: 'status = ?',
        whereArgs: [status],
      );

      final List<Invoice> result = [];
      for (var invoiceMap in invoices) {
        final List<Map<String, dynamic>> items = await db.query(
          'invoice_items',
          where: 'invoice_id = ?',
          whereArgs: [invoiceMap['id']],
        );

        invoiceMap['items'] = items;
        result.add(InvoiceModel.fromJson(invoiceMap).toEntity());
      }

      return Right(result);
    } catch (e) {
      return Left(DatabaseFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, double>> getTotalOverdueAmount() async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now().toIso8601String();
      final result = await db.rawQuery('''
        SELECT SUM(total) as total_overdue
        FROM invoices
        WHERE status = 'unpaid' AND due_date < ?
      ''', [now]);

      final totalOverdue = result.first['total_overdue'] as double? ?? 0.0;
      return Right(totalOverdue);
    } catch (e) {
      return Left(DatabaseFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, double>> getTotalUnpaidAmount() async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery('''
        SELECT SUM(total) as total_unpaid
        FROM invoices
        WHERE status = 'unpaid'
      ''');

      final totalUnpaid = result.first['total_unpaid'] as double? ?? 0.0;
      return Right(totalUnpaid);
    } catch (e) {
      return Left(DatabaseFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> updateInvoice(Invoice invoice) async {
    try {
      final db = await _databaseHelper.database;
      final invoiceModel = InvoiceModel.fromEntity(invoice);

      await db.transaction((txn) async {
        // Update invoice (without items)
        await txn.update(
          'invoices',
          {
            'invoice_number': invoiceModel.invoiceNumber,
            'creation_date': invoiceModel.creationDate.toIso8601String(),
            'due_date': invoiceModel.dueDate.toIso8601String(),
            'due_terms': invoiceModel.dueTerms,
            'po_number': invoiceModel.poNumber,
            'invoice_title': invoiceModel.invoiceTitle,
            'business_name': invoiceModel.businessName,
            'business_email': invoiceModel.businessEmail,
            'business_phone': invoiceModel.businessPhone,
            'business_address': invoiceModel.businessAddress,
            'business_website': invoiceModel.businessWebsite,
            'business_tax_id': invoiceModel.businessTaxId,
            'business_company': invoiceModel.businessCompany,
            'client_name': invoiceModel.clientName,
            'client_email': invoiceModel.clientEmail,
            'client_phone': invoiceModel.clientPhone,
            'client_address': invoiceModel.clientAddress,
            'client_company': invoiceModel.clientCompany,
            'client_tax_id': invoiceModel.clientTaxId,
            'subtotal': invoiceModel.subtotal,
            'discount': invoiceModel.discount,
            'discount_type': invoiceModel.discountType,
            'tax': invoiceModel.tax,
            'tax_type': invoiceModel.taxType,
            'shipping': invoiceModel.shipping,
            'total': invoiceModel.total,
            'currency': invoiceModel.currency,
            'language': invoiceModel.language,
            'template': invoiceModel.template,
            'status': invoiceModel.status,
            'notes': invoiceModel.notes,
            'terms': invoiceModel.terms,
            'signature': invoiceModel.signature,
            'payment_method': invoiceModel.paymentMethod,
            'updated_at': invoiceModel.updatedAt.toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [invoiceModel.id],
        );

        // Delete existing items
        await txn.delete(
          'invoice_items',
          where: 'invoice_id = ?',
          whereArgs: [invoiceModel.id],
        );

        // Insert updated items
        for (var item in invoiceModel.items) {
          await txn.insert(
            'invoice_items',
            {...item.toJson(), 'invoice_id': invoiceModel.id},
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
      });

      return const Right(true);
    } catch (e) {
      return Left(DatabaseFailure(message: e.toString()));
    }
  }
}
