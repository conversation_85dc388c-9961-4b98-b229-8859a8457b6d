import 'package:freezed_annotation/freezed_annotation.dart';

part 'invoice.freezed.dart';
part 'invoice.g.dart';

@freezed
class Invoice with _$Invoice {
  const factory Invoice({
    required String id,
    required String invoiceNumber,
    required DateTime creationDate,
    required DateTime dueDate,
    @Default('7 days') String dueTerms,
    String? poNumber,
    @Default('INVOICE') String invoiceTitle,
    required String businessName,
    required String businessEmail,
    required String businessPhone,
    required String businessAddress,
    String? businessWebsite,
    String? businessTaxId,
    String? businessCompany,
    required String clientName,
    required String clientEmail,
    required String clientPhone,
    required String clientAddress,
    String? clientCompany,
    String? clientTaxId,
    @Default([]) List<InvoiceItem> items,
    @Default(0.0) double subtotal,
    @Default(0.0) double discount,
    @Default('percentage') String discountType,
    @Default(0.0) double tax,
    @Default('percentage') String taxType,
    @Default(0.0) double shipping,
    @Default(0.0) double total,
    @Default('SAR') String currency,
    @Default('English') String language,
    @Default('Modern') String template,
    @Default('unpaid') String status,
    String? notes,
    String? terms,
    String? signature,
    String? paymentMethod,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _Invoice;

  factory Invoice.fromJson(Map<String, dynamic> json) =>
      _$InvoiceFromJson(json);
}

@freezed
class InvoiceItem with _$InvoiceItem {
  const factory InvoiceItem({
    required String id,
    required String name,
    String? description,
    @Default(1.0) double quantity,
    @Default(0.0) double unitPrice,
    @Default(0.0) double amount,
    String? notes,
    required DateTime createdAt,
  }) = _InvoiceItem;

  factory InvoiceItem.fromJson(Map<String, dynamic> json) =>
      _$InvoiceItemFromJson(json);
}
