// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'invoice.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Invoice _$InvoiceFromJson(Map<String, dynamic> json) {
  return _Invoice.fromJson(json);
}

/// @nodoc
mixin _$Invoice {
  String get id => throw _privateConstructorUsedError;
  String get invoiceNumber => throw _privateConstructorUsedError;
  DateTime get creationDate => throw _privateConstructorUsedError;
  DateTime get dueDate => throw _privateConstructorUsedError;
  String get dueTerms => throw _privateConstructorUsedError;
  String? get poNumber => throw _privateConstructorUsedError;
  String get invoiceTitle => throw _privateConstructorUsedError;
  String get businessName => throw _privateConstructorUsedError;
  String get businessEmail => throw _privateConstructorUsedError;
  String get businessPhone => throw _privateConstructorUsedError;
  String get businessAddress => throw _privateConstructorUsedError;
  String? get businessWebsite => throw _privateConstructorUsedError;
  String? get businessTaxId => throw _privateConstructorUsedError;
  String? get businessCompany => throw _privateConstructorUsedError;
  String get clientName => throw _privateConstructorUsedError;
  String get clientEmail => throw _privateConstructorUsedError;
  String get clientPhone => throw _privateConstructorUsedError;
  String get clientAddress => throw _privateConstructorUsedError;
  String? get clientCompany => throw _privateConstructorUsedError;
  String? get clientTaxId => throw _privateConstructorUsedError;
  List<InvoiceItem> get items => throw _privateConstructorUsedError;
  double get subtotal => throw _privateConstructorUsedError;
  double get discount => throw _privateConstructorUsedError;
  String get discountType => throw _privateConstructorUsedError;
  double get tax => throw _privateConstructorUsedError;
  String get taxType => throw _privateConstructorUsedError;
  double get shipping => throw _privateConstructorUsedError;
  double get total => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  String get language => throw _privateConstructorUsedError;
  String get template => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  String? get terms => throw _privateConstructorUsedError;
  String? get signature => throw _privateConstructorUsedError;
  String? get paymentMethod => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Invoice to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Invoice
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InvoiceCopyWith<Invoice> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InvoiceCopyWith<$Res> {
  factory $InvoiceCopyWith(Invoice value, $Res Function(Invoice) then) =
      _$InvoiceCopyWithImpl<$Res, Invoice>;
  @useResult
  $Res call(
      {String id,
      String invoiceNumber,
      DateTime creationDate,
      DateTime dueDate,
      String dueTerms,
      String? poNumber,
      String invoiceTitle,
      String businessName,
      String businessEmail,
      String businessPhone,
      String businessAddress,
      String? businessWebsite,
      String? businessTaxId,
      String? businessCompany,
      String clientName,
      String clientEmail,
      String clientPhone,
      String clientAddress,
      String? clientCompany,
      String? clientTaxId,
      List<InvoiceItem> items,
      double subtotal,
      double discount,
      String discountType,
      double tax,
      String taxType,
      double shipping,
      double total,
      String currency,
      String language,
      String template,
      String status,
      String? notes,
      String? terms,
      String? signature,
      String? paymentMethod,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class _$InvoiceCopyWithImpl<$Res, $Val extends Invoice>
    implements $InvoiceCopyWith<$Res> {
  _$InvoiceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Invoice
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? invoiceNumber = null,
    Object? creationDate = null,
    Object? dueDate = null,
    Object? dueTerms = null,
    Object? poNumber = freezed,
    Object? invoiceTitle = null,
    Object? businessName = null,
    Object? businessEmail = null,
    Object? businessPhone = null,
    Object? businessAddress = null,
    Object? businessWebsite = freezed,
    Object? businessTaxId = freezed,
    Object? businessCompany = freezed,
    Object? clientName = null,
    Object? clientEmail = null,
    Object? clientPhone = null,
    Object? clientAddress = null,
    Object? clientCompany = freezed,
    Object? clientTaxId = freezed,
    Object? items = null,
    Object? subtotal = null,
    Object? discount = null,
    Object? discountType = null,
    Object? tax = null,
    Object? taxType = null,
    Object? shipping = null,
    Object? total = null,
    Object? currency = null,
    Object? language = null,
    Object? template = null,
    Object? status = null,
    Object? notes = freezed,
    Object? terms = freezed,
    Object? signature = freezed,
    Object? paymentMethod = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      invoiceNumber: null == invoiceNumber
          ? _value.invoiceNumber
          : invoiceNumber // ignore: cast_nullable_to_non_nullable
              as String,
      creationDate: null == creationDate
          ? _value.creationDate
          : creationDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      dueDate: null == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      dueTerms: null == dueTerms
          ? _value.dueTerms
          : dueTerms // ignore: cast_nullable_to_non_nullable
              as String,
      poNumber: freezed == poNumber
          ? _value.poNumber
          : poNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      invoiceTitle: null == invoiceTitle
          ? _value.invoiceTitle
          : invoiceTitle // ignore: cast_nullable_to_non_nullable
              as String,
      businessName: null == businessName
          ? _value.businessName
          : businessName // ignore: cast_nullable_to_non_nullable
              as String,
      businessEmail: null == businessEmail
          ? _value.businessEmail
          : businessEmail // ignore: cast_nullable_to_non_nullable
              as String,
      businessPhone: null == businessPhone
          ? _value.businessPhone
          : businessPhone // ignore: cast_nullable_to_non_nullable
              as String,
      businessAddress: null == businessAddress
          ? _value.businessAddress
          : businessAddress // ignore: cast_nullable_to_non_nullable
              as String,
      businessWebsite: freezed == businessWebsite
          ? _value.businessWebsite
          : businessWebsite // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTaxId: freezed == businessTaxId
          ? _value.businessTaxId
          : businessTaxId // ignore: cast_nullable_to_non_nullable
              as String?,
      businessCompany: freezed == businessCompany
          ? _value.businessCompany
          : businessCompany // ignore: cast_nullable_to_non_nullable
              as String?,
      clientName: null == clientName
          ? _value.clientName
          : clientName // ignore: cast_nullable_to_non_nullable
              as String,
      clientEmail: null == clientEmail
          ? _value.clientEmail
          : clientEmail // ignore: cast_nullable_to_non_nullable
              as String,
      clientPhone: null == clientPhone
          ? _value.clientPhone
          : clientPhone // ignore: cast_nullable_to_non_nullable
              as String,
      clientAddress: null == clientAddress
          ? _value.clientAddress
          : clientAddress // ignore: cast_nullable_to_non_nullable
              as String,
      clientCompany: freezed == clientCompany
          ? _value.clientCompany
          : clientCompany // ignore: cast_nullable_to_non_nullable
              as String?,
      clientTaxId: freezed == clientTaxId
          ? _value.clientTaxId
          : clientTaxId // ignore: cast_nullable_to_non_nullable
              as String?,
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<InvoiceItem>,
      subtotal: null == subtotal
          ? _value.subtotal
          : subtotal // ignore: cast_nullable_to_non_nullable
              as double,
      discount: null == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double,
      discountType: null == discountType
          ? _value.discountType
          : discountType // ignore: cast_nullable_to_non_nullable
              as String,
      tax: null == tax
          ? _value.tax
          : tax // ignore: cast_nullable_to_non_nullable
              as double,
      taxType: null == taxType
          ? _value.taxType
          : taxType // ignore: cast_nullable_to_non_nullable
              as String,
      shipping: null == shipping
          ? _value.shipping
          : shipping // ignore: cast_nullable_to_non_nullable
              as double,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      template: null == template
          ? _value.template
          : template // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      terms: freezed == terms
          ? _value.terms
          : terms // ignore: cast_nullable_to_non_nullable
              as String?,
      signature: freezed == signature
          ? _value.signature
          : signature // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InvoiceImplCopyWith<$Res> implements $InvoiceCopyWith<$Res> {
  factory _$$InvoiceImplCopyWith(
          _$InvoiceImpl value, $Res Function(_$InvoiceImpl) then) =
      __$$InvoiceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String invoiceNumber,
      DateTime creationDate,
      DateTime dueDate,
      String dueTerms,
      String? poNumber,
      String invoiceTitle,
      String businessName,
      String businessEmail,
      String businessPhone,
      String businessAddress,
      String? businessWebsite,
      String? businessTaxId,
      String? businessCompany,
      String clientName,
      String clientEmail,
      String clientPhone,
      String clientAddress,
      String? clientCompany,
      String? clientTaxId,
      List<InvoiceItem> items,
      double subtotal,
      double discount,
      String discountType,
      double tax,
      String taxType,
      double shipping,
      double total,
      String currency,
      String language,
      String template,
      String status,
      String? notes,
      String? terms,
      String? signature,
      String? paymentMethod,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class __$$InvoiceImplCopyWithImpl<$Res>
    extends _$InvoiceCopyWithImpl<$Res, _$InvoiceImpl>
    implements _$$InvoiceImplCopyWith<$Res> {
  __$$InvoiceImplCopyWithImpl(
      _$InvoiceImpl _value, $Res Function(_$InvoiceImpl) _then)
      : super(_value, _then);

  /// Create a copy of Invoice
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? invoiceNumber = null,
    Object? creationDate = null,
    Object? dueDate = null,
    Object? dueTerms = null,
    Object? poNumber = freezed,
    Object? invoiceTitle = null,
    Object? businessName = null,
    Object? businessEmail = null,
    Object? businessPhone = null,
    Object? businessAddress = null,
    Object? businessWebsite = freezed,
    Object? businessTaxId = freezed,
    Object? businessCompany = freezed,
    Object? clientName = null,
    Object? clientEmail = null,
    Object? clientPhone = null,
    Object? clientAddress = null,
    Object? clientCompany = freezed,
    Object? clientTaxId = freezed,
    Object? items = null,
    Object? subtotal = null,
    Object? discount = null,
    Object? discountType = null,
    Object? tax = null,
    Object? taxType = null,
    Object? shipping = null,
    Object? total = null,
    Object? currency = null,
    Object? language = null,
    Object? template = null,
    Object? status = null,
    Object? notes = freezed,
    Object? terms = freezed,
    Object? signature = freezed,
    Object? paymentMethod = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$InvoiceImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      invoiceNumber: null == invoiceNumber
          ? _value.invoiceNumber
          : invoiceNumber // ignore: cast_nullable_to_non_nullable
              as String,
      creationDate: null == creationDate
          ? _value.creationDate
          : creationDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      dueDate: null == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      dueTerms: null == dueTerms
          ? _value.dueTerms
          : dueTerms // ignore: cast_nullable_to_non_nullable
              as String,
      poNumber: freezed == poNumber
          ? _value.poNumber
          : poNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      invoiceTitle: null == invoiceTitle
          ? _value.invoiceTitle
          : invoiceTitle // ignore: cast_nullable_to_non_nullable
              as String,
      businessName: null == businessName
          ? _value.businessName
          : businessName // ignore: cast_nullable_to_non_nullable
              as String,
      businessEmail: null == businessEmail
          ? _value.businessEmail
          : businessEmail // ignore: cast_nullable_to_non_nullable
              as String,
      businessPhone: null == businessPhone
          ? _value.businessPhone
          : businessPhone // ignore: cast_nullable_to_non_nullable
              as String,
      businessAddress: null == businessAddress
          ? _value.businessAddress
          : businessAddress // ignore: cast_nullable_to_non_nullable
              as String,
      businessWebsite: freezed == businessWebsite
          ? _value.businessWebsite
          : businessWebsite // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTaxId: freezed == businessTaxId
          ? _value.businessTaxId
          : businessTaxId // ignore: cast_nullable_to_non_nullable
              as String?,
      businessCompany: freezed == businessCompany
          ? _value.businessCompany
          : businessCompany // ignore: cast_nullable_to_non_nullable
              as String?,
      clientName: null == clientName
          ? _value.clientName
          : clientName // ignore: cast_nullable_to_non_nullable
              as String,
      clientEmail: null == clientEmail
          ? _value.clientEmail
          : clientEmail // ignore: cast_nullable_to_non_nullable
              as String,
      clientPhone: null == clientPhone
          ? _value.clientPhone
          : clientPhone // ignore: cast_nullable_to_non_nullable
              as String,
      clientAddress: null == clientAddress
          ? _value.clientAddress
          : clientAddress // ignore: cast_nullable_to_non_nullable
              as String,
      clientCompany: freezed == clientCompany
          ? _value.clientCompany
          : clientCompany // ignore: cast_nullable_to_non_nullable
              as String?,
      clientTaxId: freezed == clientTaxId
          ? _value.clientTaxId
          : clientTaxId // ignore: cast_nullable_to_non_nullable
              as String?,
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<InvoiceItem>,
      subtotal: null == subtotal
          ? _value.subtotal
          : subtotal // ignore: cast_nullable_to_non_nullable
              as double,
      discount: null == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double,
      discountType: null == discountType
          ? _value.discountType
          : discountType // ignore: cast_nullable_to_non_nullable
              as String,
      tax: null == tax
          ? _value.tax
          : tax // ignore: cast_nullable_to_non_nullable
              as double,
      taxType: null == taxType
          ? _value.taxType
          : taxType // ignore: cast_nullable_to_non_nullable
              as String,
      shipping: null == shipping
          ? _value.shipping
          : shipping // ignore: cast_nullable_to_non_nullable
              as double,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      template: null == template
          ? _value.template
          : template // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      terms: freezed == terms
          ? _value.terms
          : terms // ignore: cast_nullable_to_non_nullable
              as String?,
      signature: freezed == signature
          ? _value.signature
          : signature // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InvoiceImpl implements _Invoice {
  const _$InvoiceImpl(
      {required this.id,
      required this.invoiceNumber,
      required this.creationDate,
      required this.dueDate,
      this.dueTerms = '7 days',
      this.poNumber,
      this.invoiceTitle = 'INVOICE',
      required this.businessName,
      required this.businessEmail,
      required this.businessPhone,
      required this.businessAddress,
      this.businessWebsite,
      this.businessTaxId,
      this.businessCompany,
      required this.clientName,
      required this.clientEmail,
      required this.clientPhone,
      required this.clientAddress,
      this.clientCompany,
      this.clientTaxId,
      final List<InvoiceItem> items = const [],
      this.subtotal = 0.0,
      this.discount = 0.0,
      this.discountType = 'percentage',
      this.tax = 0.0,
      this.taxType = 'percentage',
      this.shipping = 0.0,
      this.total = 0.0,
      this.currency = 'SAR',
      this.language = 'English',
      this.template = 'Modern',
      this.status = 'unpaid',
      this.notes,
      this.terms,
      this.signature,
      this.paymentMethod,
      required this.createdAt,
      required this.updatedAt})
      : _items = items;

  factory _$InvoiceImpl.fromJson(Map<String, dynamic> json) =>
      _$$InvoiceImplFromJson(json);

  @override
  final String id;
  @override
  final String invoiceNumber;
  @override
  final DateTime creationDate;
  @override
  final DateTime dueDate;
  @override
  @JsonKey()
  final String dueTerms;
  @override
  final String? poNumber;
  @override
  @JsonKey()
  final String invoiceTitle;
  @override
  final String businessName;
  @override
  final String businessEmail;
  @override
  final String businessPhone;
  @override
  final String businessAddress;
  @override
  final String? businessWebsite;
  @override
  final String? businessTaxId;
  @override
  final String? businessCompany;
  @override
  final String clientName;
  @override
  final String clientEmail;
  @override
  final String clientPhone;
  @override
  final String clientAddress;
  @override
  final String? clientCompany;
  @override
  final String? clientTaxId;
  final List<InvoiceItem> _items;
  @override
  @JsonKey()
  List<InvoiceItem> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  @JsonKey()
  final double subtotal;
  @override
  @JsonKey()
  final double discount;
  @override
  @JsonKey()
  final String discountType;
  @override
  @JsonKey()
  final double tax;
  @override
  @JsonKey()
  final String taxType;
  @override
  @JsonKey()
  final double shipping;
  @override
  @JsonKey()
  final double total;
  @override
  @JsonKey()
  final String currency;
  @override
  @JsonKey()
  final String language;
  @override
  @JsonKey()
  final String template;
  @override
  @JsonKey()
  final String status;
  @override
  final String? notes;
  @override
  final String? terms;
  @override
  final String? signature;
  @override
  final String? paymentMethod;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'Invoice(id: $id, invoiceNumber: $invoiceNumber, creationDate: $creationDate, dueDate: $dueDate, dueTerms: $dueTerms, poNumber: $poNumber, invoiceTitle: $invoiceTitle, businessName: $businessName, businessEmail: $businessEmail, businessPhone: $businessPhone, businessAddress: $businessAddress, businessWebsite: $businessWebsite, businessTaxId: $businessTaxId, businessCompany: $businessCompany, clientName: $clientName, clientEmail: $clientEmail, clientPhone: $clientPhone, clientAddress: $clientAddress, clientCompany: $clientCompany, clientTaxId: $clientTaxId, items: $items, subtotal: $subtotal, discount: $discount, discountType: $discountType, tax: $tax, taxType: $taxType, shipping: $shipping, total: $total, currency: $currency, language: $language, template: $template, status: $status, notes: $notes, terms: $terms, signature: $signature, paymentMethod: $paymentMethod, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvoiceImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.invoiceNumber, invoiceNumber) ||
                other.invoiceNumber == invoiceNumber) &&
            (identical(other.creationDate, creationDate) ||
                other.creationDate == creationDate) &&
            (identical(other.dueDate, dueDate) || other.dueDate == dueDate) &&
            (identical(other.dueTerms, dueTerms) ||
                other.dueTerms == dueTerms) &&
            (identical(other.poNumber, poNumber) ||
                other.poNumber == poNumber) &&
            (identical(other.invoiceTitle, invoiceTitle) ||
                other.invoiceTitle == invoiceTitle) &&
            (identical(other.businessName, businessName) ||
                other.businessName == businessName) &&
            (identical(other.businessEmail, businessEmail) ||
                other.businessEmail == businessEmail) &&
            (identical(other.businessPhone, businessPhone) ||
                other.businessPhone == businessPhone) &&
            (identical(other.businessAddress, businessAddress) ||
                other.businessAddress == businessAddress) &&
            (identical(other.businessWebsite, businessWebsite) ||
                other.businessWebsite == businessWebsite) &&
            (identical(other.businessTaxId, businessTaxId) ||
                other.businessTaxId == businessTaxId) &&
            (identical(other.businessCompany, businessCompany) ||
                other.businessCompany == businessCompany) &&
            (identical(other.clientName, clientName) ||
                other.clientName == clientName) &&
            (identical(other.clientEmail, clientEmail) ||
                other.clientEmail == clientEmail) &&
            (identical(other.clientPhone, clientPhone) ||
                other.clientPhone == clientPhone) &&
            (identical(other.clientAddress, clientAddress) ||
                other.clientAddress == clientAddress) &&
            (identical(other.clientCompany, clientCompany) ||
                other.clientCompany == clientCompany) &&
            (identical(other.clientTaxId, clientTaxId) ||
                other.clientTaxId == clientTaxId) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.subtotal, subtotal) ||
                other.subtotal == subtotal) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.discountType, discountType) ||
                other.discountType == discountType) &&
            (identical(other.tax, tax) || other.tax == tax) &&
            (identical(other.taxType, taxType) || other.taxType == taxType) &&
            (identical(other.shipping, shipping) ||
                other.shipping == shipping) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.template, template) ||
                other.template == template) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.terms, terms) || other.terms == terms) &&
            (identical(other.signature, signature) ||
                other.signature == signature) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        invoiceNumber,
        creationDate,
        dueDate,
        dueTerms,
        poNumber,
        invoiceTitle,
        businessName,
        businessEmail,
        businessPhone,
        businessAddress,
        businessWebsite,
        businessTaxId,
        businessCompany,
        clientName,
        clientEmail,
        clientPhone,
        clientAddress,
        clientCompany,
        clientTaxId,
        const DeepCollectionEquality().hash(_items),
        subtotal,
        discount,
        discountType,
        tax,
        taxType,
        shipping,
        total,
        currency,
        language,
        template,
        status,
        notes,
        terms,
        signature,
        paymentMethod,
        createdAt,
        updatedAt
      ]);

  /// Create a copy of Invoice
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InvoiceImplCopyWith<_$InvoiceImpl> get copyWith =>
      __$$InvoiceImplCopyWithImpl<_$InvoiceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InvoiceImplToJson(
      this,
    );
  }
}

abstract class _Invoice implements Invoice {
  const factory _Invoice(
      {required final String id,
      required final String invoiceNumber,
      required final DateTime creationDate,
      required final DateTime dueDate,
      final String dueTerms,
      final String? poNumber,
      final String invoiceTitle,
      required final String businessName,
      required final String businessEmail,
      required final String businessPhone,
      required final String businessAddress,
      final String? businessWebsite,
      final String? businessTaxId,
      final String? businessCompany,
      required final String clientName,
      required final String clientEmail,
      required final String clientPhone,
      required final String clientAddress,
      final String? clientCompany,
      final String? clientTaxId,
      final List<InvoiceItem> items,
      final double subtotal,
      final double discount,
      final String discountType,
      final double tax,
      final String taxType,
      final double shipping,
      final double total,
      final String currency,
      final String language,
      final String template,
      final String status,
      final String? notes,
      final String? terms,
      final String? signature,
      final String? paymentMethod,
      required final DateTime createdAt,
      required final DateTime updatedAt}) = _$InvoiceImpl;

  factory _Invoice.fromJson(Map<String, dynamic> json) = _$InvoiceImpl.fromJson;

  @override
  String get id;
  @override
  String get invoiceNumber;
  @override
  DateTime get creationDate;
  @override
  DateTime get dueDate;
  @override
  String get dueTerms;
  @override
  String? get poNumber;
  @override
  String get invoiceTitle;
  @override
  String get businessName;
  @override
  String get businessEmail;
  @override
  String get businessPhone;
  @override
  String get businessAddress;
  @override
  String? get businessWebsite;
  @override
  String? get businessTaxId;
  @override
  String? get businessCompany;
  @override
  String get clientName;
  @override
  String get clientEmail;
  @override
  String get clientPhone;
  @override
  String get clientAddress;
  @override
  String? get clientCompany;
  @override
  String? get clientTaxId;
  @override
  List<InvoiceItem> get items;
  @override
  double get subtotal;
  @override
  double get discount;
  @override
  String get discountType;
  @override
  double get tax;
  @override
  String get taxType;
  @override
  double get shipping;
  @override
  double get total;
  @override
  String get currency;
  @override
  String get language;
  @override
  String get template;
  @override
  String get status;
  @override
  String? get notes;
  @override
  String? get terms;
  @override
  String? get signature;
  @override
  String? get paymentMethod;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of Invoice
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InvoiceImplCopyWith<_$InvoiceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

InvoiceItem _$InvoiceItemFromJson(Map<String, dynamic> json) {
  return _InvoiceItem.fromJson(json);
}

/// @nodoc
mixin _$InvoiceItem {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  double get quantity => throw _privateConstructorUsedError;
  double get unitPrice => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// Serializes this InvoiceItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of InvoiceItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InvoiceItemCopyWith<InvoiceItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InvoiceItemCopyWith<$Res> {
  factory $InvoiceItemCopyWith(
          InvoiceItem value, $Res Function(InvoiceItem) then) =
      _$InvoiceItemCopyWithImpl<$Res, InvoiceItem>;
  @useResult
  $Res call(
      {String id,
      String name,
      String? description,
      double quantity,
      double unitPrice,
      double amount,
      String? notes,
      DateTime createdAt});
}

/// @nodoc
class _$InvoiceItemCopyWithImpl<$Res, $Val extends InvoiceItem>
    implements $InvoiceItemCopyWith<$Res> {
  _$InvoiceItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InvoiceItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? quantity = null,
    Object? unitPrice = null,
    Object? amount = null,
    Object? notes = freezed,
    Object? createdAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double,
      unitPrice: null == unitPrice
          ? _value.unitPrice
          : unitPrice // ignore: cast_nullable_to_non_nullable
              as double,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InvoiceItemImplCopyWith<$Res>
    implements $InvoiceItemCopyWith<$Res> {
  factory _$$InvoiceItemImplCopyWith(
          _$InvoiceItemImpl value, $Res Function(_$InvoiceItemImpl) then) =
      __$$InvoiceItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String? description,
      double quantity,
      double unitPrice,
      double amount,
      String? notes,
      DateTime createdAt});
}

/// @nodoc
class __$$InvoiceItemImplCopyWithImpl<$Res>
    extends _$InvoiceItemCopyWithImpl<$Res, _$InvoiceItemImpl>
    implements _$$InvoiceItemImplCopyWith<$Res> {
  __$$InvoiceItemImplCopyWithImpl(
      _$InvoiceItemImpl _value, $Res Function(_$InvoiceItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? quantity = null,
    Object? unitPrice = null,
    Object? amount = null,
    Object? notes = freezed,
    Object? createdAt = null,
  }) {
    return _then(_$InvoiceItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double,
      unitPrice: null == unitPrice
          ? _value.unitPrice
          : unitPrice // ignore: cast_nullable_to_non_nullable
              as double,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InvoiceItemImpl implements _InvoiceItem {
  const _$InvoiceItemImpl(
      {required this.id,
      required this.name,
      this.description,
      this.quantity = 1.0,
      this.unitPrice = 0.0,
      this.amount = 0.0,
      this.notes,
      required this.createdAt});

  factory _$InvoiceItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$InvoiceItemImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  @JsonKey()
  final double quantity;
  @override
  @JsonKey()
  final double unitPrice;
  @override
  @JsonKey()
  final double amount;
  @override
  final String? notes;
  @override
  final DateTime createdAt;

  @override
  String toString() {
    return 'InvoiceItem(id: $id, name: $name, description: $description, quantity: $quantity, unitPrice: $unitPrice, amount: $amount, notes: $notes, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvoiceItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.unitPrice, unitPrice) ||
                other.unitPrice == unitPrice) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, description, quantity,
      unitPrice, amount, notes, createdAt);

  /// Create a copy of InvoiceItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InvoiceItemImplCopyWith<_$InvoiceItemImpl> get copyWith =>
      __$$InvoiceItemImplCopyWithImpl<_$InvoiceItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InvoiceItemImplToJson(
      this,
    );
  }
}

abstract class _InvoiceItem implements InvoiceItem {
  const factory _InvoiceItem(
      {required final String id,
      required final String name,
      final String? description,
      final double quantity,
      final double unitPrice,
      final double amount,
      final String? notes,
      required final DateTime createdAt}) = _$InvoiceItemImpl;

  factory _InvoiceItem.fromJson(Map<String, dynamic> json) =
      _$InvoiceItemImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  double get quantity;
  @override
  double get unitPrice;
  @override
  double get amount;
  @override
  String? get notes;
  @override
  DateTime get createdAt;

  /// Create a copy of InvoiceItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InvoiceItemImplCopyWith<_$InvoiceItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
