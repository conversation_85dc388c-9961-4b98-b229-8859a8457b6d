// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$InvoiceImpl _$$InvoiceImplFromJson(Map<String, dynamic> json) =>
    _$InvoiceImpl(
      id: json['id'] as String,
      invoiceNumber: json['invoiceNumber'] as String,
      creationDate: DateTime.parse(json['creationDate'] as String),
      dueDate: DateTime.parse(json['dueDate'] as String),
      dueTerms: json['dueTerms'] as String? ?? '7 days',
      poNumber: json['poNumber'] as String?,
      invoiceTitle: json['invoiceTitle'] as String? ?? 'INVOICE',
      businessName: json['businessName'] as String,
      businessEmail: json['businessEmail'] as String,
      businessPhone: json['businessPhone'] as String,
      businessAddress: json['businessAddress'] as String,
      businessWebsite: json['businessWebsite'] as String?,
      businessTaxId: json['businessTaxId'] as String?,
      businessCompany: json['businessCompany'] as String?,
      clientName: json['clientName'] as String,
      clientEmail: json['clientEmail'] as String,
      clientPhone: json['clientPhone'] as String,
      clientAddress: json['clientAddress'] as String,
      clientCompany: json['clientCompany'] as String?,
      clientTaxId: json['clientTaxId'] as String?,
      items: (json['items'] as List<dynamic>?)
              ?.map((e) => InvoiceItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      subtotal: (json['subtotal'] as num?)?.toDouble() ?? 0.0,
      discount: (json['discount'] as num?)?.toDouble() ?? 0.0,
      discountType: json['discountType'] as String? ?? 'percentage',
      tax: (json['tax'] as num?)?.toDouble() ?? 0.0,
      taxType: json['taxType'] as String? ?? 'percentage',
      shipping: (json['shipping'] as num?)?.toDouble() ?? 0.0,
      total: (json['total'] as num?)?.toDouble() ?? 0.0,
      currency: json['currency'] as String? ?? 'SAR',
      language: json['language'] as String? ?? 'English',
      template: json['template'] as String? ?? 'Modern',
      status: json['status'] as String? ?? 'unpaid',
      notes: json['notes'] as String?,
      terms: json['terms'] as String?,
      signature: json['signature'] as String?,
      paymentMethod: json['paymentMethod'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$InvoiceImplToJson(_$InvoiceImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'invoiceNumber': instance.invoiceNumber,
      'creationDate': instance.creationDate.toIso8601String(),
      'dueDate': instance.dueDate.toIso8601String(),
      'dueTerms': instance.dueTerms,
      'poNumber': instance.poNumber,
      'invoiceTitle': instance.invoiceTitle,
      'businessName': instance.businessName,
      'businessEmail': instance.businessEmail,
      'businessPhone': instance.businessPhone,
      'businessAddress': instance.businessAddress,
      'businessWebsite': instance.businessWebsite,
      'businessTaxId': instance.businessTaxId,
      'businessCompany': instance.businessCompany,
      'clientName': instance.clientName,
      'clientEmail': instance.clientEmail,
      'clientPhone': instance.clientPhone,
      'clientAddress': instance.clientAddress,
      'clientCompany': instance.clientCompany,
      'clientTaxId': instance.clientTaxId,
      'items': instance.items,
      'subtotal': instance.subtotal,
      'discount': instance.discount,
      'discountType': instance.discountType,
      'tax': instance.tax,
      'taxType': instance.taxType,
      'shipping': instance.shipping,
      'total': instance.total,
      'currency': instance.currency,
      'language': instance.language,
      'template': instance.template,
      'status': instance.status,
      'notes': instance.notes,
      'terms': instance.terms,
      'signature': instance.signature,
      'paymentMethod': instance.paymentMethod,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

_$InvoiceItemImpl _$$InvoiceItemImplFromJson(Map<String, dynamic> json) =>
    _$InvoiceItemImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      quantity: (json['quantity'] as num?)?.toDouble() ?? 1.0,
      unitPrice: (json['unitPrice'] as num?)?.toDouble() ?? 0.0,
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$InvoiceItemImplToJson(_$InvoiceItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'quantity': instance.quantity,
      'unitPrice': instance.unitPrice,
      'amount': instance.amount,
      'notes': instance.notes,
      'createdAt': instance.createdAt.toIso8601String(),
    };
