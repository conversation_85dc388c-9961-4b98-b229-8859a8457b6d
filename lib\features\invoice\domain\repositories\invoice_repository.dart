import 'package:dartz/dartz.dart';
import 'package:invoice/core/error/failures.dart';
import 'package:invoice/features/invoice/domain/entities/invoice.dart';

abstract class InvoiceRepository {
  Future<Either<Failure, List<Invoice>>> getAllInvoices();
  Future<Either<Failure, Invoice>> getInvoiceById(String id);
  Future<Either<Failure, String>> createInvoice(Invoice invoice);
  Future<Either<Failure, bool>> updateInvoice(Invoice invoice);
  Future<Either<Failure, bool>> deleteInvoice(String id);
  Future<Either<Failure, List<Invoice>>> getInvoicesByStatus(String status);
  Future<Either<Failure, double>> getTotalUnpaidAmount();
  Future<Either<Failure, double>> getTotalOverdueAmount();
}
