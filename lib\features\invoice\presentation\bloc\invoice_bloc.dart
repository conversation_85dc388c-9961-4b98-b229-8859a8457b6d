import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:invoice/features/invoice/domain/entities/invoice.dart';
import 'package:invoice/features/invoice/domain/repositories/invoice_repository.dart';

part 'invoice_bloc.freezed.dart';
part 'invoice_event.dart';
part 'invoice_state.dart';

class InvoiceBloc extends Bloc<InvoiceEvent, InvoiceState> {
  final InvoiceRepository _repository;

  InvoiceBloc({required InvoiceRepository repository})
      : _repository = repository,
        super(const InvoiceState.initial()) {
    on<InvoiceEvent>((event, emit) async {
      await event.map(
        started: (_) async => await _onStarted(emit),
        createInvoice: (e) async => await _onCreateInvoice(e, emit),
        updateInvoice: (e) async => await _onUpdateInvoice(e, emit),
        deleteInvoice: (e) async => await _onDeleteInvoice(e, emit),
        getInvoiceById: (e) async => await _onGetInvoiceById(e, emit),
        getInvoicesByStatus: (e) async => await _onGetInvoicesByStatus(e, emit),
      );
    });
  }

  Future<void> _onStarted(Emitter<InvoiceState> emit) async {
    emit(const InvoiceState.loading());
    final result = await _repository.getAllInvoices();
    result.fold(
      (failure) => emit(InvoiceState.error(failure.message)),
      (invoices) => emit(InvoiceState.loaded(invoices)),
    );
  }

  Future<void> _onCreateInvoice(
    _CreateInvoice event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(const InvoiceState.loading());
    final result = await _repository.createInvoice(event.invoice);
    result.fold(
      (failure) => emit(InvoiceState.error(failure.message)),
      (id) => emit(InvoiceState.created(id)),
    );
  }

  Future<void> _onUpdateInvoice(
    _UpdateInvoice event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(const InvoiceState.loading());
    final result = await _repository.updateInvoice(event.invoice);
    result.fold(
      (failure) => emit(InvoiceState.error(failure.message)),
      (success) => emit(const InvoiceState.updated()),
    );
  }

  Future<void> _onDeleteInvoice(
    _DeleteInvoice event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(const InvoiceState.loading());
    final result = await _repository.deleteInvoice(event.id);
    result.fold(
      (failure) => emit(InvoiceState.error(failure.message)),
      (success) => emit(const InvoiceState.deleted()),
    );
  }

  Future<void> _onGetInvoiceById(
    _GetInvoiceById event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(const InvoiceState.loading());
    final result = await _repository.getInvoiceById(event.id);
    result.fold(
      (failure) => emit(InvoiceState.error(failure.message)),
      (invoice) => emit(InvoiceState.loadedSingle(invoice)),
    );
  }

  Future<void> _onGetInvoicesByStatus(
    _GetInvoicesByStatus event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(const InvoiceState.loading());
    final result = await _repository.getInvoicesByStatus(event.status);
    result.fold(
      (failure) => emit(InvoiceState.error(failure.message)),
      (invoices) => emit(InvoiceState.loaded(invoices)),
    );
  }
}
