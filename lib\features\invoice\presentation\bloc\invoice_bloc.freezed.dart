// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'invoice_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$InvoiceEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(Invoice invoice) createInvoice,
    required TResult Function(Invoice invoice) updateInvoice,
    required TResult Function(String id) deleteInvoice,
    required TResult Function(String id) getInvoiceById,
    required TResult Function(String status) getInvoicesByStatus,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(Invoice invoice)? createInvoice,
    TResult? Function(Invoice invoice)? updateInvoice,
    TResult? Function(String id)? deleteInvoice,
    TResult? Function(String id)? getInvoiceById,
    TResult? Function(String status)? getInvoicesByStatus,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(Invoice invoice)? createInvoice,
    TResult Function(Invoice invoice)? updateInvoice,
    TResult Function(String id)? deleteInvoice,
    TResult Function(String id)? getInvoiceById,
    TResult Function(String status)? getInvoicesByStatus,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_CreateInvoice value) createInvoice,
    required TResult Function(_UpdateInvoice value) updateInvoice,
    required TResult Function(_DeleteInvoice value) deleteInvoice,
    required TResult Function(_GetInvoiceById value) getInvoiceById,
    required TResult Function(_GetInvoicesByStatus value) getInvoicesByStatus,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_CreateInvoice value)? createInvoice,
    TResult? Function(_UpdateInvoice value)? updateInvoice,
    TResult? Function(_DeleteInvoice value)? deleteInvoice,
    TResult? Function(_GetInvoiceById value)? getInvoiceById,
    TResult? Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_CreateInvoice value)? createInvoice,
    TResult Function(_UpdateInvoice value)? updateInvoice,
    TResult Function(_DeleteInvoice value)? deleteInvoice,
    TResult Function(_GetInvoiceById value)? getInvoiceById,
    TResult Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InvoiceEventCopyWith<$Res> {
  factory $InvoiceEventCopyWith(
          InvoiceEvent value, $Res Function(InvoiceEvent) then) =
      _$InvoiceEventCopyWithImpl<$Res, InvoiceEvent>;
}

/// @nodoc
class _$InvoiceEventCopyWithImpl<$Res, $Val extends InvoiceEvent>
    implements $InvoiceEventCopyWith<$Res> {
  _$InvoiceEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$InvoiceEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl();

  @override
  String toString() {
    return 'InvoiceEvent.started()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(Invoice invoice) createInvoice,
    required TResult Function(Invoice invoice) updateInvoice,
    required TResult Function(String id) deleteInvoice,
    required TResult Function(String id) getInvoiceById,
    required TResult Function(String status) getInvoicesByStatus,
  }) {
    return started();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(Invoice invoice)? createInvoice,
    TResult? Function(Invoice invoice)? updateInvoice,
    TResult? Function(String id)? deleteInvoice,
    TResult? Function(String id)? getInvoiceById,
    TResult? Function(String status)? getInvoicesByStatus,
  }) {
    return started?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(Invoice invoice)? createInvoice,
    TResult Function(Invoice invoice)? updateInvoice,
    TResult Function(String id)? deleteInvoice,
    TResult Function(String id)? getInvoiceById,
    TResult Function(String status)? getInvoicesByStatus,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_CreateInvoice value) createInvoice,
    required TResult Function(_UpdateInvoice value) updateInvoice,
    required TResult Function(_DeleteInvoice value) deleteInvoice,
    required TResult Function(_GetInvoiceById value) getInvoiceById,
    required TResult Function(_GetInvoicesByStatus value) getInvoicesByStatus,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_CreateInvoice value)? createInvoice,
    TResult? Function(_UpdateInvoice value)? updateInvoice,
    TResult? Function(_DeleteInvoice value)? deleteInvoice,
    TResult? Function(_GetInvoiceById value)? getInvoiceById,
    TResult? Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_CreateInvoice value)? createInvoice,
    TResult Function(_UpdateInvoice value)? updateInvoice,
    TResult Function(_DeleteInvoice value)? deleteInvoice,
    TResult Function(_GetInvoiceById value)? getInvoiceById,
    TResult Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements InvoiceEvent {
  const factory _Started() = _$StartedImpl;
}

/// @nodoc
abstract class _$$CreateInvoiceImplCopyWith<$Res> {
  factory _$$CreateInvoiceImplCopyWith(
          _$CreateInvoiceImpl value, $Res Function(_$CreateInvoiceImpl) then) =
      __$$CreateInvoiceImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Invoice invoice});

  $InvoiceCopyWith<$Res> get invoice;
}

/// @nodoc
class __$$CreateInvoiceImplCopyWithImpl<$Res>
    extends _$InvoiceEventCopyWithImpl<$Res, _$CreateInvoiceImpl>
    implements _$$CreateInvoiceImplCopyWith<$Res> {
  __$$CreateInvoiceImplCopyWithImpl(
      _$CreateInvoiceImpl _value, $Res Function(_$CreateInvoiceImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? invoice = null,
  }) {
    return _then(_$CreateInvoiceImpl(
      null == invoice
          ? _value.invoice
          : invoice // ignore: cast_nullable_to_non_nullable
              as Invoice,
    ));
  }

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InvoiceCopyWith<$Res> get invoice {
    return $InvoiceCopyWith<$Res>(_value.invoice, (value) {
      return _then(_value.copyWith(invoice: value));
    });
  }
}

/// @nodoc

class _$CreateInvoiceImpl implements _CreateInvoice {
  const _$CreateInvoiceImpl(this.invoice);

  @override
  final Invoice invoice;

  @override
  String toString() {
    return 'InvoiceEvent.createInvoice(invoice: $invoice)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateInvoiceImpl &&
            (identical(other.invoice, invoice) || other.invoice == invoice));
  }

  @override
  int get hashCode => Object.hash(runtimeType, invoice);

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateInvoiceImplCopyWith<_$CreateInvoiceImpl> get copyWith =>
      __$$CreateInvoiceImplCopyWithImpl<_$CreateInvoiceImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(Invoice invoice) createInvoice,
    required TResult Function(Invoice invoice) updateInvoice,
    required TResult Function(String id) deleteInvoice,
    required TResult Function(String id) getInvoiceById,
    required TResult Function(String status) getInvoicesByStatus,
  }) {
    return createInvoice(invoice);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(Invoice invoice)? createInvoice,
    TResult? Function(Invoice invoice)? updateInvoice,
    TResult? Function(String id)? deleteInvoice,
    TResult? Function(String id)? getInvoiceById,
    TResult? Function(String status)? getInvoicesByStatus,
  }) {
    return createInvoice?.call(invoice);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(Invoice invoice)? createInvoice,
    TResult Function(Invoice invoice)? updateInvoice,
    TResult Function(String id)? deleteInvoice,
    TResult Function(String id)? getInvoiceById,
    TResult Function(String status)? getInvoicesByStatus,
    required TResult orElse(),
  }) {
    if (createInvoice != null) {
      return createInvoice(invoice);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_CreateInvoice value) createInvoice,
    required TResult Function(_UpdateInvoice value) updateInvoice,
    required TResult Function(_DeleteInvoice value) deleteInvoice,
    required TResult Function(_GetInvoiceById value) getInvoiceById,
    required TResult Function(_GetInvoicesByStatus value) getInvoicesByStatus,
  }) {
    return createInvoice(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_CreateInvoice value)? createInvoice,
    TResult? Function(_UpdateInvoice value)? updateInvoice,
    TResult? Function(_DeleteInvoice value)? deleteInvoice,
    TResult? Function(_GetInvoiceById value)? getInvoiceById,
    TResult? Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
  }) {
    return createInvoice?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_CreateInvoice value)? createInvoice,
    TResult Function(_UpdateInvoice value)? updateInvoice,
    TResult Function(_DeleteInvoice value)? deleteInvoice,
    TResult Function(_GetInvoiceById value)? getInvoiceById,
    TResult Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
    required TResult orElse(),
  }) {
    if (createInvoice != null) {
      return createInvoice(this);
    }
    return orElse();
  }
}

abstract class _CreateInvoice implements InvoiceEvent {
  const factory _CreateInvoice(final Invoice invoice) = _$CreateInvoiceImpl;

  Invoice get invoice;

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateInvoiceImplCopyWith<_$CreateInvoiceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateInvoiceImplCopyWith<$Res> {
  factory _$$UpdateInvoiceImplCopyWith(
          _$UpdateInvoiceImpl value, $Res Function(_$UpdateInvoiceImpl) then) =
      __$$UpdateInvoiceImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Invoice invoice});

  $InvoiceCopyWith<$Res> get invoice;
}

/// @nodoc
class __$$UpdateInvoiceImplCopyWithImpl<$Res>
    extends _$InvoiceEventCopyWithImpl<$Res, _$UpdateInvoiceImpl>
    implements _$$UpdateInvoiceImplCopyWith<$Res> {
  __$$UpdateInvoiceImplCopyWithImpl(
      _$UpdateInvoiceImpl _value, $Res Function(_$UpdateInvoiceImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? invoice = null,
  }) {
    return _then(_$UpdateInvoiceImpl(
      null == invoice
          ? _value.invoice
          : invoice // ignore: cast_nullable_to_non_nullable
              as Invoice,
    ));
  }

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InvoiceCopyWith<$Res> get invoice {
    return $InvoiceCopyWith<$Res>(_value.invoice, (value) {
      return _then(_value.copyWith(invoice: value));
    });
  }
}

/// @nodoc

class _$UpdateInvoiceImpl implements _UpdateInvoice {
  const _$UpdateInvoiceImpl(this.invoice);

  @override
  final Invoice invoice;

  @override
  String toString() {
    return 'InvoiceEvent.updateInvoice(invoice: $invoice)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateInvoiceImpl &&
            (identical(other.invoice, invoice) || other.invoice == invoice));
  }

  @override
  int get hashCode => Object.hash(runtimeType, invoice);

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateInvoiceImplCopyWith<_$UpdateInvoiceImpl> get copyWith =>
      __$$UpdateInvoiceImplCopyWithImpl<_$UpdateInvoiceImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(Invoice invoice) createInvoice,
    required TResult Function(Invoice invoice) updateInvoice,
    required TResult Function(String id) deleteInvoice,
    required TResult Function(String id) getInvoiceById,
    required TResult Function(String status) getInvoicesByStatus,
  }) {
    return updateInvoice(invoice);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(Invoice invoice)? createInvoice,
    TResult? Function(Invoice invoice)? updateInvoice,
    TResult? Function(String id)? deleteInvoice,
    TResult? Function(String id)? getInvoiceById,
    TResult? Function(String status)? getInvoicesByStatus,
  }) {
    return updateInvoice?.call(invoice);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(Invoice invoice)? createInvoice,
    TResult Function(Invoice invoice)? updateInvoice,
    TResult Function(String id)? deleteInvoice,
    TResult Function(String id)? getInvoiceById,
    TResult Function(String status)? getInvoicesByStatus,
    required TResult orElse(),
  }) {
    if (updateInvoice != null) {
      return updateInvoice(invoice);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_CreateInvoice value) createInvoice,
    required TResult Function(_UpdateInvoice value) updateInvoice,
    required TResult Function(_DeleteInvoice value) deleteInvoice,
    required TResult Function(_GetInvoiceById value) getInvoiceById,
    required TResult Function(_GetInvoicesByStatus value) getInvoicesByStatus,
  }) {
    return updateInvoice(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_CreateInvoice value)? createInvoice,
    TResult? Function(_UpdateInvoice value)? updateInvoice,
    TResult? Function(_DeleteInvoice value)? deleteInvoice,
    TResult? Function(_GetInvoiceById value)? getInvoiceById,
    TResult? Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
  }) {
    return updateInvoice?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_CreateInvoice value)? createInvoice,
    TResult Function(_UpdateInvoice value)? updateInvoice,
    TResult Function(_DeleteInvoice value)? deleteInvoice,
    TResult Function(_GetInvoiceById value)? getInvoiceById,
    TResult Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
    required TResult orElse(),
  }) {
    if (updateInvoice != null) {
      return updateInvoice(this);
    }
    return orElse();
  }
}

abstract class _UpdateInvoice implements InvoiceEvent {
  const factory _UpdateInvoice(final Invoice invoice) = _$UpdateInvoiceImpl;

  Invoice get invoice;

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateInvoiceImplCopyWith<_$UpdateInvoiceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteInvoiceImplCopyWith<$Res> {
  factory _$$DeleteInvoiceImplCopyWith(
          _$DeleteInvoiceImpl value, $Res Function(_$DeleteInvoiceImpl) then) =
      __$$DeleteInvoiceImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$DeleteInvoiceImplCopyWithImpl<$Res>
    extends _$InvoiceEventCopyWithImpl<$Res, _$DeleteInvoiceImpl>
    implements _$$DeleteInvoiceImplCopyWith<$Res> {
  __$$DeleteInvoiceImplCopyWithImpl(
      _$DeleteInvoiceImpl _value, $Res Function(_$DeleteInvoiceImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$DeleteInvoiceImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteInvoiceImpl implements _DeleteInvoice {
  const _$DeleteInvoiceImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'InvoiceEvent.deleteInvoice(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteInvoiceImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteInvoiceImplCopyWith<_$DeleteInvoiceImpl> get copyWith =>
      __$$DeleteInvoiceImplCopyWithImpl<_$DeleteInvoiceImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(Invoice invoice) createInvoice,
    required TResult Function(Invoice invoice) updateInvoice,
    required TResult Function(String id) deleteInvoice,
    required TResult Function(String id) getInvoiceById,
    required TResult Function(String status) getInvoicesByStatus,
  }) {
    return deleteInvoice(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(Invoice invoice)? createInvoice,
    TResult? Function(Invoice invoice)? updateInvoice,
    TResult? Function(String id)? deleteInvoice,
    TResult? Function(String id)? getInvoiceById,
    TResult? Function(String status)? getInvoicesByStatus,
  }) {
    return deleteInvoice?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(Invoice invoice)? createInvoice,
    TResult Function(Invoice invoice)? updateInvoice,
    TResult Function(String id)? deleteInvoice,
    TResult Function(String id)? getInvoiceById,
    TResult Function(String status)? getInvoicesByStatus,
    required TResult orElse(),
  }) {
    if (deleteInvoice != null) {
      return deleteInvoice(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_CreateInvoice value) createInvoice,
    required TResult Function(_UpdateInvoice value) updateInvoice,
    required TResult Function(_DeleteInvoice value) deleteInvoice,
    required TResult Function(_GetInvoiceById value) getInvoiceById,
    required TResult Function(_GetInvoicesByStatus value) getInvoicesByStatus,
  }) {
    return deleteInvoice(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_CreateInvoice value)? createInvoice,
    TResult? Function(_UpdateInvoice value)? updateInvoice,
    TResult? Function(_DeleteInvoice value)? deleteInvoice,
    TResult? Function(_GetInvoiceById value)? getInvoiceById,
    TResult? Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
  }) {
    return deleteInvoice?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_CreateInvoice value)? createInvoice,
    TResult Function(_UpdateInvoice value)? updateInvoice,
    TResult Function(_DeleteInvoice value)? deleteInvoice,
    TResult Function(_GetInvoiceById value)? getInvoiceById,
    TResult Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
    required TResult orElse(),
  }) {
    if (deleteInvoice != null) {
      return deleteInvoice(this);
    }
    return orElse();
  }
}

abstract class _DeleteInvoice implements InvoiceEvent {
  const factory _DeleteInvoice(final String id) = _$DeleteInvoiceImpl;

  String get id;

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteInvoiceImplCopyWith<_$DeleteInvoiceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetInvoiceByIdImplCopyWith<$Res> {
  factory _$$GetInvoiceByIdImplCopyWith(_$GetInvoiceByIdImpl value,
          $Res Function(_$GetInvoiceByIdImpl) then) =
      __$$GetInvoiceByIdImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$GetInvoiceByIdImplCopyWithImpl<$Res>
    extends _$InvoiceEventCopyWithImpl<$Res, _$GetInvoiceByIdImpl>
    implements _$$GetInvoiceByIdImplCopyWith<$Res> {
  __$$GetInvoiceByIdImplCopyWithImpl(
      _$GetInvoiceByIdImpl _value, $Res Function(_$GetInvoiceByIdImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$GetInvoiceByIdImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetInvoiceByIdImpl implements _GetInvoiceById {
  const _$GetInvoiceByIdImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'InvoiceEvent.getInvoiceById(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetInvoiceByIdImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetInvoiceByIdImplCopyWith<_$GetInvoiceByIdImpl> get copyWith =>
      __$$GetInvoiceByIdImplCopyWithImpl<_$GetInvoiceByIdImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(Invoice invoice) createInvoice,
    required TResult Function(Invoice invoice) updateInvoice,
    required TResult Function(String id) deleteInvoice,
    required TResult Function(String id) getInvoiceById,
    required TResult Function(String status) getInvoicesByStatus,
  }) {
    return getInvoiceById(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(Invoice invoice)? createInvoice,
    TResult? Function(Invoice invoice)? updateInvoice,
    TResult? Function(String id)? deleteInvoice,
    TResult? Function(String id)? getInvoiceById,
    TResult? Function(String status)? getInvoicesByStatus,
  }) {
    return getInvoiceById?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(Invoice invoice)? createInvoice,
    TResult Function(Invoice invoice)? updateInvoice,
    TResult Function(String id)? deleteInvoice,
    TResult Function(String id)? getInvoiceById,
    TResult Function(String status)? getInvoicesByStatus,
    required TResult orElse(),
  }) {
    if (getInvoiceById != null) {
      return getInvoiceById(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_CreateInvoice value) createInvoice,
    required TResult Function(_UpdateInvoice value) updateInvoice,
    required TResult Function(_DeleteInvoice value) deleteInvoice,
    required TResult Function(_GetInvoiceById value) getInvoiceById,
    required TResult Function(_GetInvoicesByStatus value) getInvoicesByStatus,
  }) {
    return getInvoiceById(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_CreateInvoice value)? createInvoice,
    TResult? Function(_UpdateInvoice value)? updateInvoice,
    TResult? Function(_DeleteInvoice value)? deleteInvoice,
    TResult? Function(_GetInvoiceById value)? getInvoiceById,
    TResult? Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
  }) {
    return getInvoiceById?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_CreateInvoice value)? createInvoice,
    TResult Function(_UpdateInvoice value)? updateInvoice,
    TResult Function(_DeleteInvoice value)? deleteInvoice,
    TResult Function(_GetInvoiceById value)? getInvoiceById,
    TResult Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
    required TResult orElse(),
  }) {
    if (getInvoiceById != null) {
      return getInvoiceById(this);
    }
    return orElse();
  }
}

abstract class _GetInvoiceById implements InvoiceEvent {
  const factory _GetInvoiceById(final String id) = _$GetInvoiceByIdImpl;

  String get id;

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetInvoiceByIdImplCopyWith<_$GetInvoiceByIdImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetInvoicesByStatusImplCopyWith<$Res> {
  factory _$$GetInvoicesByStatusImplCopyWith(_$GetInvoicesByStatusImpl value,
          $Res Function(_$GetInvoicesByStatusImpl) then) =
      __$$GetInvoicesByStatusImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String status});
}

/// @nodoc
class __$$GetInvoicesByStatusImplCopyWithImpl<$Res>
    extends _$InvoiceEventCopyWithImpl<$Res, _$GetInvoicesByStatusImpl>
    implements _$$GetInvoicesByStatusImplCopyWith<$Res> {
  __$$GetInvoicesByStatusImplCopyWithImpl(_$GetInvoicesByStatusImpl _value,
      $Res Function(_$GetInvoicesByStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
  }) {
    return _then(_$GetInvoicesByStatusImpl(
      null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetInvoicesByStatusImpl implements _GetInvoicesByStatus {
  const _$GetInvoicesByStatusImpl(this.status);

  @override
  final String status;

  @override
  String toString() {
    return 'InvoiceEvent.getInvoicesByStatus(status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetInvoicesByStatusImpl &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, status);

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetInvoicesByStatusImplCopyWith<_$GetInvoicesByStatusImpl> get copyWith =>
      __$$GetInvoicesByStatusImplCopyWithImpl<_$GetInvoicesByStatusImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(Invoice invoice) createInvoice,
    required TResult Function(Invoice invoice) updateInvoice,
    required TResult Function(String id) deleteInvoice,
    required TResult Function(String id) getInvoiceById,
    required TResult Function(String status) getInvoicesByStatus,
  }) {
    return getInvoicesByStatus(status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(Invoice invoice)? createInvoice,
    TResult? Function(Invoice invoice)? updateInvoice,
    TResult? Function(String id)? deleteInvoice,
    TResult? Function(String id)? getInvoiceById,
    TResult? Function(String status)? getInvoicesByStatus,
  }) {
    return getInvoicesByStatus?.call(status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(Invoice invoice)? createInvoice,
    TResult Function(Invoice invoice)? updateInvoice,
    TResult Function(String id)? deleteInvoice,
    TResult Function(String id)? getInvoiceById,
    TResult Function(String status)? getInvoicesByStatus,
    required TResult orElse(),
  }) {
    if (getInvoicesByStatus != null) {
      return getInvoicesByStatus(status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_CreateInvoice value) createInvoice,
    required TResult Function(_UpdateInvoice value) updateInvoice,
    required TResult Function(_DeleteInvoice value) deleteInvoice,
    required TResult Function(_GetInvoiceById value) getInvoiceById,
    required TResult Function(_GetInvoicesByStatus value) getInvoicesByStatus,
  }) {
    return getInvoicesByStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_CreateInvoice value)? createInvoice,
    TResult? Function(_UpdateInvoice value)? updateInvoice,
    TResult? Function(_DeleteInvoice value)? deleteInvoice,
    TResult? Function(_GetInvoiceById value)? getInvoiceById,
    TResult? Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
  }) {
    return getInvoicesByStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_CreateInvoice value)? createInvoice,
    TResult Function(_UpdateInvoice value)? updateInvoice,
    TResult Function(_DeleteInvoice value)? deleteInvoice,
    TResult Function(_GetInvoiceById value)? getInvoiceById,
    TResult Function(_GetInvoicesByStatus value)? getInvoicesByStatus,
    required TResult orElse(),
  }) {
    if (getInvoicesByStatus != null) {
      return getInvoicesByStatus(this);
    }
    return orElse();
  }
}

abstract class _GetInvoicesByStatus implements InvoiceEvent {
  const factory _GetInvoicesByStatus(final String status) =
      _$GetInvoicesByStatusImpl;

  String get status;

  /// Create a copy of InvoiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetInvoicesByStatusImplCopyWith<_$GetInvoicesByStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$InvoiceState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<Invoice> invoices) loaded,
    required TResult Function(Invoice invoice) loadedSingle,
    required TResult Function(String id) created,
    required TResult Function() updated,
    required TResult Function() deleted,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<Invoice> invoices)? loaded,
    TResult? Function(Invoice invoice)? loadedSingle,
    TResult? Function(String id)? created,
    TResult? Function()? updated,
    TResult? Function()? deleted,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<Invoice> invoices)? loaded,
    TResult Function(Invoice invoice)? loadedSingle,
    TResult Function(String id)? created,
    TResult Function()? updated,
    TResult Function()? deleted,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_LoadedSingle value) loadedSingle,
    required TResult Function(_Created value) created,
    required TResult Function(_Updated value) updated,
    required TResult Function(_Deleted value) deleted,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_LoadedSingle value)? loadedSingle,
    TResult? Function(_Created value)? created,
    TResult? Function(_Updated value)? updated,
    TResult? Function(_Deleted value)? deleted,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_LoadedSingle value)? loadedSingle,
    TResult Function(_Created value)? created,
    TResult Function(_Updated value)? updated,
    TResult Function(_Deleted value)? deleted,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InvoiceStateCopyWith<$Res> {
  factory $InvoiceStateCopyWith(
          InvoiceState value, $Res Function(InvoiceState) then) =
      _$InvoiceStateCopyWithImpl<$Res, InvoiceState>;
}

/// @nodoc
class _$InvoiceStateCopyWithImpl<$Res, $Val extends InvoiceState>
    implements $InvoiceStateCopyWith<$Res> {
  _$InvoiceStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$InvoiceStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'InvoiceState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<Invoice> invoices) loaded,
    required TResult Function(Invoice invoice) loadedSingle,
    required TResult Function(String id) created,
    required TResult Function() updated,
    required TResult Function() deleted,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<Invoice> invoices)? loaded,
    TResult? Function(Invoice invoice)? loadedSingle,
    TResult? Function(String id)? created,
    TResult? Function()? updated,
    TResult? Function()? deleted,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<Invoice> invoices)? loaded,
    TResult Function(Invoice invoice)? loadedSingle,
    TResult Function(String id)? created,
    TResult Function()? updated,
    TResult Function()? deleted,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_LoadedSingle value) loadedSingle,
    required TResult Function(_Created value) created,
    required TResult Function(_Updated value) updated,
    required TResult Function(_Deleted value) deleted,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_LoadedSingle value)? loadedSingle,
    TResult? Function(_Created value)? created,
    TResult? Function(_Updated value)? updated,
    TResult? Function(_Deleted value)? deleted,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_LoadedSingle value)? loadedSingle,
    TResult Function(_Created value)? created,
    TResult Function(_Updated value)? updated,
    TResult Function(_Deleted value)? deleted,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements InvoiceState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$InvoiceStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'InvoiceState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<Invoice> invoices) loaded,
    required TResult Function(Invoice invoice) loadedSingle,
    required TResult Function(String id) created,
    required TResult Function() updated,
    required TResult Function() deleted,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<Invoice> invoices)? loaded,
    TResult? Function(Invoice invoice)? loadedSingle,
    TResult? Function(String id)? created,
    TResult? Function()? updated,
    TResult? Function()? deleted,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<Invoice> invoices)? loaded,
    TResult Function(Invoice invoice)? loadedSingle,
    TResult Function(String id)? created,
    TResult Function()? updated,
    TResult Function()? deleted,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_LoadedSingle value) loadedSingle,
    required TResult Function(_Created value) created,
    required TResult Function(_Updated value) updated,
    required TResult Function(_Deleted value) deleted,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_LoadedSingle value)? loadedSingle,
    TResult? Function(_Created value)? created,
    TResult? Function(_Updated value)? updated,
    TResult? Function(_Deleted value)? deleted,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_LoadedSingle value)? loadedSingle,
    TResult Function(_Created value)? created,
    TResult Function(_Updated value)? updated,
    TResult Function(_Deleted value)? deleted,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements InvoiceState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl value, $Res Function(_$ErrorImpl) then) =
      __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$InvoiceStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl _value, $Res Function(_$ErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'InvoiceState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<Invoice> invoices) loaded,
    required TResult Function(Invoice invoice) loadedSingle,
    required TResult Function(String id) created,
    required TResult Function() updated,
    required TResult Function() deleted,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<Invoice> invoices)? loaded,
    TResult? Function(Invoice invoice)? loadedSingle,
    TResult? Function(String id)? created,
    TResult? Function()? updated,
    TResult? Function()? deleted,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<Invoice> invoices)? loaded,
    TResult Function(Invoice invoice)? loadedSingle,
    TResult Function(String id)? created,
    TResult Function()? updated,
    TResult Function()? deleted,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_LoadedSingle value) loadedSingle,
    required TResult Function(_Created value) created,
    required TResult Function(_Updated value) updated,
    required TResult Function(_Deleted value) deleted,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_LoadedSingle value)? loadedSingle,
    TResult? Function(_Created value)? created,
    TResult? Function(_Updated value)? updated,
    TResult? Function(_Deleted value)? deleted,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_LoadedSingle value)? loadedSingle,
    TResult Function(_Created value)? created,
    TResult Function(_Updated value)? updated,
    TResult Function(_Deleted value)? deleted,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements InvoiceState {
  const factory _Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
          _$LoadedImpl value, $Res Function(_$LoadedImpl) then) =
      __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Invoice> invoices});
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$InvoiceStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
      _$LoadedImpl _value, $Res Function(_$LoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? invoices = null,
  }) {
    return _then(_$LoadedImpl(
      null == invoices
          ? _value._invoices
          : invoices // ignore: cast_nullable_to_non_nullable
              as List<Invoice>,
    ));
  }
}

/// @nodoc

class _$LoadedImpl implements _Loaded {
  const _$LoadedImpl(final List<Invoice> invoices) : _invoices = invoices;

  final List<Invoice> _invoices;
  @override
  List<Invoice> get invoices {
    if (_invoices is EqualUnmodifiableListView) return _invoices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_invoices);
  }

  @override
  String toString() {
    return 'InvoiceState.loaded(invoices: $invoices)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            const DeepCollectionEquality().equals(other._invoices, _invoices));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_invoices));

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<Invoice> invoices) loaded,
    required TResult Function(Invoice invoice) loadedSingle,
    required TResult Function(String id) created,
    required TResult Function() updated,
    required TResult Function() deleted,
  }) {
    return loaded(invoices);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<Invoice> invoices)? loaded,
    TResult? Function(Invoice invoice)? loadedSingle,
    TResult? Function(String id)? created,
    TResult? Function()? updated,
    TResult? Function()? deleted,
  }) {
    return loaded?.call(invoices);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<Invoice> invoices)? loaded,
    TResult Function(Invoice invoice)? loadedSingle,
    TResult Function(String id)? created,
    TResult Function()? updated,
    TResult Function()? deleted,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(invoices);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_LoadedSingle value) loadedSingle,
    required TResult Function(_Created value) created,
    required TResult Function(_Updated value) updated,
    required TResult Function(_Deleted value) deleted,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_LoadedSingle value)? loadedSingle,
    TResult? Function(_Created value)? created,
    TResult? Function(_Updated value)? updated,
    TResult? Function(_Deleted value)? deleted,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_LoadedSingle value)? loadedSingle,
    TResult Function(_Created value)? created,
    TResult Function(_Updated value)? updated,
    TResult Function(_Deleted value)? deleted,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _Loaded implements InvoiceState {
  const factory _Loaded(final List<Invoice> invoices) = _$LoadedImpl;

  List<Invoice> get invoices;

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadedSingleImplCopyWith<$Res> {
  factory _$$LoadedSingleImplCopyWith(
          _$LoadedSingleImpl value, $Res Function(_$LoadedSingleImpl) then) =
      __$$LoadedSingleImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Invoice invoice});

  $InvoiceCopyWith<$Res> get invoice;
}

/// @nodoc
class __$$LoadedSingleImplCopyWithImpl<$Res>
    extends _$InvoiceStateCopyWithImpl<$Res, _$LoadedSingleImpl>
    implements _$$LoadedSingleImplCopyWith<$Res> {
  __$$LoadedSingleImplCopyWithImpl(
      _$LoadedSingleImpl _value, $Res Function(_$LoadedSingleImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? invoice = null,
  }) {
    return _then(_$LoadedSingleImpl(
      null == invoice
          ? _value.invoice
          : invoice // ignore: cast_nullable_to_non_nullable
              as Invoice,
    ));
  }

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InvoiceCopyWith<$Res> get invoice {
    return $InvoiceCopyWith<$Res>(_value.invoice, (value) {
      return _then(_value.copyWith(invoice: value));
    });
  }
}

/// @nodoc

class _$LoadedSingleImpl implements _LoadedSingle {
  const _$LoadedSingleImpl(this.invoice);

  @override
  final Invoice invoice;

  @override
  String toString() {
    return 'InvoiceState.loadedSingle(invoice: $invoice)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedSingleImpl &&
            (identical(other.invoice, invoice) || other.invoice == invoice));
  }

  @override
  int get hashCode => Object.hash(runtimeType, invoice);

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedSingleImplCopyWith<_$LoadedSingleImpl> get copyWith =>
      __$$LoadedSingleImplCopyWithImpl<_$LoadedSingleImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<Invoice> invoices) loaded,
    required TResult Function(Invoice invoice) loadedSingle,
    required TResult Function(String id) created,
    required TResult Function() updated,
    required TResult Function() deleted,
  }) {
    return loadedSingle(invoice);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<Invoice> invoices)? loaded,
    TResult? Function(Invoice invoice)? loadedSingle,
    TResult? Function(String id)? created,
    TResult? Function()? updated,
    TResult? Function()? deleted,
  }) {
    return loadedSingle?.call(invoice);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<Invoice> invoices)? loaded,
    TResult Function(Invoice invoice)? loadedSingle,
    TResult Function(String id)? created,
    TResult Function()? updated,
    TResult Function()? deleted,
    required TResult orElse(),
  }) {
    if (loadedSingle != null) {
      return loadedSingle(invoice);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_LoadedSingle value) loadedSingle,
    required TResult Function(_Created value) created,
    required TResult Function(_Updated value) updated,
    required TResult Function(_Deleted value) deleted,
  }) {
    return loadedSingle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_LoadedSingle value)? loadedSingle,
    TResult? Function(_Created value)? created,
    TResult? Function(_Updated value)? updated,
    TResult? Function(_Deleted value)? deleted,
  }) {
    return loadedSingle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_LoadedSingle value)? loadedSingle,
    TResult Function(_Created value)? created,
    TResult Function(_Updated value)? updated,
    TResult Function(_Deleted value)? deleted,
    required TResult orElse(),
  }) {
    if (loadedSingle != null) {
      return loadedSingle(this);
    }
    return orElse();
  }
}

abstract class _LoadedSingle implements InvoiceState {
  const factory _LoadedSingle(final Invoice invoice) = _$LoadedSingleImpl;

  Invoice get invoice;

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedSingleImplCopyWith<_$LoadedSingleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreatedImplCopyWith<$Res> {
  factory _$$CreatedImplCopyWith(
          _$CreatedImpl value, $Res Function(_$CreatedImpl) then) =
      __$$CreatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$CreatedImplCopyWithImpl<$Res>
    extends _$InvoiceStateCopyWithImpl<$Res, _$CreatedImpl>
    implements _$$CreatedImplCopyWith<$Res> {
  __$$CreatedImplCopyWithImpl(
      _$CreatedImpl _value, $Res Function(_$CreatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$CreatedImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CreatedImpl implements _Created {
  const _$CreatedImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'InvoiceState.created(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreatedImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreatedImplCopyWith<_$CreatedImpl> get copyWith =>
      __$$CreatedImplCopyWithImpl<_$CreatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<Invoice> invoices) loaded,
    required TResult Function(Invoice invoice) loadedSingle,
    required TResult Function(String id) created,
    required TResult Function() updated,
    required TResult Function() deleted,
  }) {
    return created(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<Invoice> invoices)? loaded,
    TResult? Function(Invoice invoice)? loadedSingle,
    TResult? Function(String id)? created,
    TResult? Function()? updated,
    TResult? Function()? deleted,
  }) {
    return created?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<Invoice> invoices)? loaded,
    TResult Function(Invoice invoice)? loadedSingle,
    TResult Function(String id)? created,
    TResult Function()? updated,
    TResult Function()? deleted,
    required TResult orElse(),
  }) {
    if (created != null) {
      return created(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_LoadedSingle value) loadedSingle,
    required TResult Function(_Created value) created,
    required TResult Function(_Updated value) updated,
    required TResult Function(_Deleted value) deleted,
  }) {
    return created(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_LoadedSingle value)? loadedSingle,
    TResult? Function(_Created value)? created,
    TResult? Function(_Updated value)? updated,
    TResult? Function(_Deleted value)? deleted,
  }) {
    return created?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_LoadedSingle value)? loadedSingle,
    TResult Function(_Created value)? created,
    TResult Function(_Updated value)? updated,
    TResult Function(_Deleted value)? deleted,
    required TResult orElse(),
  }) {
    if (created != null) {
      return created(this);
    }
    return orElse();
  }
}

abstract class _Created implements InvoiceState {
  const factory _Created(final String id) = _$CreatedImpl;

  String get id;

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreatedImplCopyWith<_$CreatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatedImplCopyWith<$Res> {
  factory _$$UpdatedImplCopyWith(
          _$UpdatedImpl value, $Res Function(_$UpdatedImpl) then) =
      __$$UpdatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UpdatedImplCopyWithImpl<$Res>
    extends _$InvoiceStateCopyWithImpl<$Res, _$UpdatedImpl>
    implements _$$UpdatedImplCopyWith<$Res> {
  __$$UpdatedImplCopyWithImpl(
      _$UpdatedImpl _value, $Res Function(_$UpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UpdatedImpl implements _Updated {
  const _$UpdatedImpl();

  @override
  String toString() {
    return 'InvoiceState.updated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UpdatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<Invoice> invoices) loaded,
    required TResult Function(Invoice invoice) loadedSingle,
    required TResult Function(String id) created,
    required TResult Function() updated,
    required TResult Function() deleted,
  }) {
    return updated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<Invoice> invoices)? loaded,
    TResult? Function(Invoice invoice)? loadedSingle,
    TResult? Function(String id)? created,
    TResult? Function()? updated,
    TResult? Function()? deleted,
  }) {
    return updated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<Invoice> invoices)? loaded,
    TResult Function(Invoice invoice)? loadedSingle,
    TResult Function(String id)? created,
    TResult Function()? updated,
    TResult Function()? deleted,
    required TResult orElse(),
  }) {
    if (updated != null) {
      return updated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_LoadedSingle value) loadedSingle,
    required TResult Function(_Created value) created,
    required TResult Function(_Updated value) updated,
    required TResult Function(_Deleted value) deleted,
  }) {
    return updated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_LoadedSingle value)? loadedSingle,
    TResult? Function(_Created value)? created,
    TResult? Function(_Updated value)? updated,
    TResult? Function(_Deleted value)? deleted,
  }) {
    return updated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_LoadedSingle value)? loadedSingle,
    TResult Function(_Created value)? created,
    TResult Function(_Updated value)? updated,
    TResult Function(_Deleted value)? deleted,
    required TResult orElse(),
  }) {
    if (updated != null) {
      return updated(this);
    }
    return orElse();
  }
}

abstract class _Updated implements InvoiceState {
  const factory _Updated() = _$UpdatedImpl;
}

/// @nodoc
abstract class _$$DeletedImplCopyWith<$Res> {
  factory _$$DeletedImplCopyWith(
          _$DeletedImpl value, $Res Function(_$DeletedImpl) then) =
      __$$DeletedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeletedImplCopyWithImpl<$Res>
    extends _$InvoiceStateCopyWithImpl<$Res, _$DeletedImpl>
    implements _$$DeletedImplCopyWith<$Res> {
  __$$DeletedImplCopyWithImpl(
      _$DeletedImpl _value, $Res Function(_$DeletedImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeletedImpl implements _Deleted {
  const _$DeletedImpl();

  @override
  String toString() {
    return 'InvoiceState.deleted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DeletedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<Invoice> invoices) loaded,
    required TResult Function(Invoice invoice) loadedSingle,
    required TResult Function(String id) created,
    required TResult Function() updated,
    required TResult Function() deleted,
  }) {
    return deleted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<Invoice> invoices)? loaded,
    TResult? Function(Invoice invoice)? loadedSingle,
    TResult? Function(String id)? created,
    TResult? Function()? updated,
    TResult? Function()? deleted,
  }) {
    return deleted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<Invoice> invoices)? loaded,
    TResult Function(Invoice invoice)? loadedSingle,
    TResult Function(String id)? created,
    TResult Function()? updated,
    TResult Function()? deleted,
    required TResult orElse(),
  }) {
    if (deleted != null) {
      return deleted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_LoadedSingle value) loadedSingle,
    required TResult Function(_Created value) created,
    required TResult Function(_Updated value) updated,
    required TResult Function(_Deleted value) deleted,
  }) {
    return deleted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_LoadedSingle value)? loadedSingle,
    TResult? Function(_Created value)? created,
    TResult? Function(_Updated value)? updated,
    TResult? Function(_Deleted value)? deleted,
  }) {
    return deleted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_LoadedSingle value)? loadedSingle,
    TResult Function(_Created value)? created,
    TResult Function(_Updated value)? updated,
    TResult Function(_Deleted value)? deleted,
    required TResult orElse(),
  }) {
    if (deleted != null) {
      return deleted(this);
    }
    return orElse();
  }
}

abstract class _Deleted implements InvoiceState {
  const factory _Deleted() = _$DeletedImpl;
}
