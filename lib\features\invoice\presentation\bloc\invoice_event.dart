part of 'invoice_bloc.dart';

@freezed
class InvoiceEvent with _$InvoiceEvent {
  const factory InvoiceEvent.started() = _Started;
  const factory InvoiceEvent.createInvoice(Invoice invoice) = _CreateInvoice;
  const factory InvoiceEvent.updateInvoice(Invoice invoice) = _UpdateInvoice;
  const factory InvoiceEvent.deleteInvoice(String id) = _DeleteInvoice;
  const factory InvoiceEvent.getInvoiceById(String id) = _GetInvoiceById;
  const factory InvoiceEvent.getInvoicesByStatus(String status) =
      _GetInvoicesByStatus;
}
