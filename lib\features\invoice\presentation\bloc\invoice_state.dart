part of 'invoice_bloc.dart';

@freezed
class InvoiceState with _$InvoiceState {
  const factory InvoiceState.initial() = _Initial;
  const factory InvoiceState.loading() = _Loading;
  const factory InvoiceState.error(String message) = _Error;
  const factory InvoiceState.loaded(List<Invoice> invoices) = _Loaded;
  const factory InvoiceState.loadedSingle(Invoice invoice) = _LoadedSingle;
  const factory InvoiceState.created(String id) = _Created;
  const factory InvoiceState.updated() = _Updated;
  const factory InvoiceState.deleted() = _Deleted;
}
