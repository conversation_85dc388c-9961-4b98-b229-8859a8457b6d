import 'package:flutter/material.dart';

class CalculationDialog extends StatefulWidget {
  final String title;
  final String type; // 'discount', 'tax', 'shipping'
  final double currentAmount;
  final double currentPercentage;
  final double subtotal;

  const CalculationDialog({
    super.key,
    required this.title,
    required this.type,
    required this.currentAmount,
    required this.currentPercentage,
    required this.subtotal,
  });

  @override
  State<CalculationDialog> createState() => _CalculationDialogState();
}

class _CalculationDialogState extends State<CalculationDialog> {
  final _amountController = TextEditingController();
  final _percentageController = TextEditingController();
  bool _isPercentage = true;

  @override
  void initState() {
    super.initState();
    if (widget.currentPercentage > 0) {
      _percentageController.text = widget.currentPercentage.toString();
      _isPercentage = true;
    } else if (widget.currentAmount > 0) {
      _amountController.text = widget.currentAmount.toString();
      _isPercentage = false;
    }
    
    _amountController.addListener(_updateFromAmount);
    _percentageController.addListener(_updateFromPercentage);
  }

  void _updateFromAmount() {
    if (!_isPercentage && _amountController.text.isNotEmpty) {
      final amount = double.tryParse(_amountController.text) ?? 0;
      final percentage = widget.subtotal > 0 ? (amount / widget.subtotal) * 100 : 0;
      _percentageController.removeListener(_updateFromPercentage);
      _percentageController.text = percentage.toStringAsFixed(1);
      _percentageController.addListener(_updateFromPercentage);
    }
  }

  void _updateFromPercentage() {
    if (_isPercentage && _percentageController.text.isNotEmpty) {
      final percentage = double.tryParse(_percentageController.text) ?? 0;
      final amount = (widget.subtotal * percentage) / 100;
      _amountController.removeListener(_updateFromAmount);
      _amountController.text = amount.toStringAsFixed(2);
      _amountController.addListener(_updateFromAmount);
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _percentageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getIcon(),
                  color: const Color(0xFF4285F4),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    widget.title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Toggle between percentage and amount
            if (widget.type != 'shipping') ...[
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _isPercentage = true;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          decoration: BoxDecoration(
                            color: _isPercentage ? const Color(0xFF4285F4) : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Percentage',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: _isPercentage ? Colors.white : Colors.grey.shade600,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _isPercentage = false;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          decoration: BoxDecoration(
                            color: !_isPercentage ? const Color(0xFF4285F4) : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Amount',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: !_isPercentage ? Colors.white : Colors.grey.shade600,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
            ],

            // Input fields
            if (_isPercentage && widget.type != 'shipping') ...[
              _buildTextField(
                controller: _percentageController,
                label: 'Percentage (%)',
                hint: '0.0',
                suffix: '%',
                keyboardType: TextInputType.number,
              ),
            ] else ...[
              _buildTextField(
                controller: _amountController,
                label: 'Amount',
                hint: '0.00',
                suffix: 'ج',
                keyboardType: TextInputType.number,
              ),
            ],

            const SizedBox(height: 24),

            // Summary
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Subtotal:'),
                      Text('${widget.subtotal.toStringAsFixed(2)}ج'),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('${widget.title}:'),
                      Text('${_getCurrentAmount().toStringAsFixed(2)}ج'),
                    ],
                  ),
                  const Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Total:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        '${_getTotal().toStringAsFixed(2)}ج',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _save,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4285F4),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Save'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required String suffix,
    required TextInputType keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          decoration: InputDecoration(
            hintText: hint,
            suffixText: suffix,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF4285F4)),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
          ),
        ),
      ],
    );
  }

  IconData _getIcon() {
    switch (widget.type) {
      case 'discount':
        return Icons.percent;
      case 'tax':
        return Icons.account_balance;
      case 'shipping':
        return Icons.local_shipping;
      default:
        return Icons.calculate;
    }
  }

  double _getCurrentAmount() {
    if (_isPercentage && widget.type != 'shipping') {
      final percentage = double.tryParse(_percentageController.text) ?? 0;
      return (widget.subtotal * percentage) / 100;
    } else {
      return double.tryParse(_amountController.text) ?? 0;
    }
  }

  double _getTotal() {
    final amount = _getCurrentAmount();
    if (widget.type == 'discount') {
      return widget.subtotal - amount;
    } else {
      return widget.subtotal + amount;
    }
  }

  void _save() {
    final amount = _getCurrentAmount();
    final percentage = _isPercentage && widget.type != 'shipping' 
        ? (double.tryParse(_percentageController.text) ?? 0)
        : (widget.subtotal > 0 ? (amount / widget.subtotal) * 100 : 0);

    Navigator.pop(context, {
      'amount': amount,
      'percentage': percentage,
    });
  }
}
