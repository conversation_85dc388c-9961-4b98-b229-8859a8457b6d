import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';
import 'package:invoice/features/invoice/domain/entities/invoice.dart';
import 'package:invoice/core/database/database_helper.dart';
import 'package:invoice/core/services/settings_service.dart';
import 'package:invoice/features/invoice/presentation/screens/select_items_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/business_info_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/client_info_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/add_item_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/calculation_dialog.dart';
import 'package:invoice/features/invoice/presentation/screens/invoice_preview_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/language_selection_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/template_selection_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/currency_selection_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/signature_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/terms_conditions_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/payment_method_screen.dart';

class CreateInvoiceScreen extends StatefulWidget {
  const CreateInvoiceScreen({super.key});

  @override
  State<CreateInvoiceScreen> createState() => _CreateInvoiceScreenState();
}

class _CreateInvoiceScreenState extends State<CreateInvoiceScreen> {
  final ScrollController _scrollController = ScrollController();
  final SettingsService _settingsService = SettingsService();

  // Form data
  String invoiceNumber = 'INV00001';
  DateTime createdDate = DateTime.now();
  DateTime dueDate = DateTime.now().add(const Duration(days: 30));
  String language = 'English';
  String template = 'Default';
  String businessInfo = 'Add Business';
  String clientInfo = 'Add Client';
  Map<String, dynamic>? businessData;
  Map<String, dynamic>? clientData;
  List<InvoiceItemData> items = [];
  double subtotal = 0.00;
  double discount = 0.00;
  double discountPercent = 0.0;
  double tax = 0.00;
  double taxPercent = 0.0;
  double shipping = 0.00;
  double total = 0.00;
  String currency = 'SAR ج';
  String signature = 'Add Signature';
  String termsConditions = '';
  String paymentMethod = '';
  List<String> attachments = [];

  @override
  void initState() {
    super.initState();
    _initializeWithSettings();
  }

  Future<void> _initializeWithSettings() async {
    try {
      await _settingsService.initialize();

      // Load settings and apply them
      final defaultCurrency = await _settingsService.getDefaultCurrency();
      final dueTerms = await _settingsService.getDueTerms();
      final taxRate = await _settingsService.getTaxRate();
      final defaultPaymentMethod = await _settingsService.getPaymentMethod();
      final defaultTerms = await _settingsService.getTermsConditions();
      final defaultTemplate = await _settingsService.getDefaultTemplate();
      final defaultLanguage = await _settingsService.getLanguage();

      // Load business information from settings
      final businessInfo = await _settingsService.getBusinessInfo();

      // Calculate due date based on settings
      final dueDateFromSettings =
          await _settingsService.calculateDueDate(createdDate);

      if (mounted) {
        setState(() {
          currency = '$defaultCurrency ج';
          dueDate = dueDateFromSettings;
          taxPercent = taxRate;
          tax = (subtotal * taxRate / 100);
          paymentMethod = defaultPaymentMethod;
          termsConditions = defaultTerms;
          template = defaultTemplate;
          language = defaultLanguage;

          // Set business data if available
          if (businessInfo['name']!.isNotEmpty) {
            businessData = businessInfo;
            this.businessInfo = businessInfo['name']!;
          }
        });

        _calculateTotals();
      }
    } catch (e) {
      print('Error initializing settings: $e');
    }
  }

  void _calculateTotals() {
    subtotal = items.fold(0.0, (sum, item) => sum + item.total);

    // Calculate tax based on current tax percentage
    tax = subtotal * taxPercent / 100;

    // Calculate discount based on current discount percentage
    discount = subtotal * discountPercent / 100;

    total = subtotal + tax + shipping - discount;
  }

  Map<String, dynamic> _getInvoiceData() {
    return {
      'invoiceNumber': invoiceNumber,
      'createdDate': createdDate,
      'dueDate': dueDate,
      'businessInfo':
          businessInfo != 'Add Business' ? {'name': businessInfo} : null,
      'clientInfo': clientInfo != 'Add Client' ? {'name': clientInfo} : null,
      'items': items
          .map((item) => {
                'name': item.name,
                'description': item.description,
                'quantity': item.quantity,
                'unitPrice': item.unitPrice,
                'total': item.total,
              })
          .toList(),
      'subtotal': subtotal,
      'discount': discount,
      'tax': tax,
      'shipping': shipping,
      'total': total,
      'currency': currency,
      'signature': signature,
      'termsConditions': termsConditions,
      'paymentMethod': paymentMethod,
    };
  }

  Map<String, dynamic> _getBusinessData() {
    // إذا كانت هناك بيانات محفوظة، استخدمها
    if (businessData != null) {
      return {
        'name': businessData!['name'] ?? 'شركة الفواتير المحدودة',
        'email': businessData!['email'] ?? '<EMAIL>',
        'phone': businessData!['phone'] ?? '+966501234567',
        'address':
            businessData!['address'] ?? 'الرياض، المملكة العربية السعودية',
        'website': businessData!['website'] ?? 'www.invoicecompany.com',
        'taxId': businessData!['taxNumber'] ?? '*********',
        'company': businessData!['name'] ?? 'شركة الفواتير المحدودة',
      };
    }

    // بيانات افتراضية إذا لم تكن هناك بيانات محفوظة
    return {
      'name': 'شركة الفواتير المحدودة',
      'email': '<EMAIL>',
      'phone': '+966501234567',
      'address': 'الرياض، المملكة العربية السعودية',
      'website': 'www.invoicecompany.com',
      'taxId': '*********',
      'company': 'شركة الفواتير المحدودة',
    };
  }

  Map<String, dynamic> _getClientData() {
    // إذا كانت هناك بيانات محفوظة، استخدمها
    if (clientData != null) {
      return {
        'name': clientData!['name'] ?? 'العميل الافتراضي',
        'email': clientData!['email'] ?? '<EMAIL>',
        'phone': clientData!['phone'] ?? '+966509876543',
        'address': clientData!['address'] ?? 'عنوان العميل',
        'company': clientData!['company'] ?? 'شركة العميل',
        'taxId': clientData!['taxNumber'] ?? '*********',
      };
    }

    // بيانات افتراضية إذا لم تكن هناك بيانات محفوظة
    return {
      'name': 'العميل الافتراضي',
      'email': '<EMAIL>',
      'phone': '+966509876543',
      'address': 'عنوان العميل',
      'company': 'شركة العميل',
      'taxId': '*********',
    };
  }

  InvoiceItemData _convertToInvoiceItem(Map<String, dynamic> item) {
    final quantity =
        double.tryParse(item['quantity']?.toString() ?? '1') ?? 1.0;
    final unitPrice =
        double.tryParse(item['unit_price']?.toString() ?? '0') ?? 0.0;
    final amount = double.tryParse(item['amount']?.toString() ?? '0') ??
        (quantity * unitPrice);

    return InvoiceItemData(
      name: item['name']?.toString() ?? '',
      description: item['description']?.toString() ?? '',
      quantity: quantity.toInt(),
      unitPrice: unitPrice,
      total: amount,
    );
  }

  Future<void> _saveInvoice() async {
    if (items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add at least one item to the invoice'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (businessInfo == 'Add Business') {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add business information'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (clientInfo == 'Add Client') {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add client information'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      // Create invoice entity
      final now = DateTime.now();
      const uuid = Uuid();

      final invoiceItems = items
          .map((item) => InvoiceItem(
                id: uuid.v4(),
                name: item.name,
                description: item.description,
                quantity: item.quantity.toDouble(),
                unitPrice: item.unitPrice,
                amount: item.total,
                createdAt: now,
              ))
          .toList();

      // استخراج معلومات الأعمال الحقيقية
      final businessData = _getBusinessData();
      final clientData = _getClientData();

      final invoice = Invoice(
        id: uuid.v4(),
        invoiceNumber: invoiceNumber,
        creationDate: createdDate,
        dueDate: dueDate,
        dueTerms: '7 days',
        invoiceTitle: 'INVOICE',
        // معلومات الأعمال الحقيقية
        businessName: businessData['name'] ?? 'Business Name',
        businessEmail: businessData['email'] ?? '<EMAIL>',
        businessPhone: businessData['phone'] ?? '+*********0',
        businessAddress: businessData['address'] ?? 'Business Address',
        businessWebsite: businessData['website'],
        businessTaxId: businessData['taxId'],
        businessCompany: businessData['company'],
        // معلومات العميل الحقيقية
        clientName: clientData['name'] ?? 'Client Name',
        clientEmail: clientData['email'] ?? '<EMAIL>',
        clientPhone: clientData['phone'] ?? '+*********0',
        clientAddress: clientData['address'] ?? 'Client Address',
        clientCompany: clientData['company'],
        clientTaxId: clientData['taxId'],
        // العناصر والحسابات
        items: invoiceItems,
        subtotal: subtotal,
        discount: discount,
        discountType: 'percentage',
        tax: tax,
        taxType: 'percentage',
        shipping: shipping,
        total: total,
        currency: currency.replaceAll(' ج', ''),
        language: language,
        template: template,
        status: 'unpaid',
        terms: termsConditions.isNotEmpty ? termsConditions : null,
        signature: signature != 'Add Signature' ? signature : null,
        paymentMethod: paymentMethod.isNotEmpty ? paymentMethod : null,
        createdAt: now,
        updatedAt: now,
      );

      // Save to database directly
      print('🔄 Attempting to save invoice to database...');
      print('📋 Invoice ID: ${invoice.id}');
      print('📋 Invoice Number: ${invoice.invoiceNumber}');
      print('👤 Client Name: ${invoice.clientName}');
      print('🏢 Business Name: ${invoice.businessName}');
      print('📦 Items Count: ${invoice.items.length}');
      print('💰 Total: ${invoice.total}');

      final db = await DatabaseHelper.instance.database;

      // Save invoice to database
      final invoiceData = {
        'id': invoice.id,
        'invoice_number': invoice.invoiceNumber,
        'creation_date': invoice.creationDate.toIso8601String(),
        'due_date': invoice.dueDate.toIso8601String(),
        'due_terms': invoice.dueTerms ?? '7 days',
        'po_number': '',
        'invoice_title': invoice.invoiceTitle ?? 'INVOICE',
        'business_name': invoice.businessName,
        'business_email': invoice.businessEmail,
        'business_phone': invoice.businessPhone,
        'business_address': invoice.businessAddress,
        'business_website': invoice.businessWebsite ?? '',
        'business_tax_id': invoice.businessTaxId ?? '',
        'business_company': invoice.businessCompany ?? '',
        'client_name': invoice.clientName,
        'client_email': invoice.clientEmail,
        'client_phone': invoice.clientPhone,
        'client_address': invoice.clientAddress,
        'client_company': invoice.clientCompany ?? '',
        'client_tax_id': invoice.clientTaxId ?? '',
        'subtotal': invoice.subtotal,
        'discount': invoice.discount,
        'discount_type': invoice.discountType ?? 'percentage',
        'tax': invoice.tax,
        'tax_type': invoice.taxType ?? 'percentage',
        'shipping': invoice.shipping,
        'total': invoice.total,
        'currency': invoice.currency,
        'language': invoice.language ?? 'Arabic',
        'template': invoice.template ?? 'Modern',
        'status': invoice.status ?? 'unpaid',
        'notes': '',
        'terms': invoice.terms ?? '',
        'signature': invoice.signature ?? '',
        'payment_method': invoice.paymentMethod ?? '',
        'created_at': invoice.createdAt.toIso8601String(),
        'updated_at': invoice.updatedAt.toIso8601String(),
      };

      await db.insert('invoices', invoiceData);
      print('✅ Invoice saved to database');

      // Save invoice items
      for (final item in invoice.items) {
        final itemData = {
          'id': item.id,
          'invoice_id': invoice.id,
          'name': item.name,
          'description': item.description ?? '',
          'quantity': item.quantity,
          'unit_price': item.unitPrice,
          'amount': item.amount,
          'notes': '',
          'created_at': invoice.createdAt.toIso8601String(),
        };
        await db.insert('invoice_items', itemData);
      }
      print('✅ Invoice items saved to database');

      // Save business info if not exists
      final businessInfoExists = await db.query(
        'business_info',
        where: 'id = ?',
        whereArgs: [1], // Use INTEGER instead of string
      );

      if (businessInfoExists.isEmpty) {
        final businessInfoData = {
          'id': 1, // Use INTEGER instead of string
          'name': invoice.businessName,
          'email': invoice.businessEmail,
          'phone': invoice.businessPhone,
          'address': invoice.businessAddress,
          'website': invoice.businessWebsite ?? '',
          'tax_id': invoice.businessTaxId ?? '',
          'company': invoice.businessCompany ?? '',
          'logo_path': '',
          'created_at': invoice.createdAt.toIso8601String(),
          'updated_at': invoice.updatedAt.toIso8601String(),
        };
        await db.insert('business_info', businessInfoData);
        print('✅ Business info saved to database');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'تم حفظ الفاتورة بنجاح! رقم الفاتورة: ${invoice.invoiceNumber}'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
        Navigator.pop(context, _getInvoiceData());
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving invoice: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _previewInvoice() {
    if (items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add at least one item to preview the invoice'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final invoiceData = _getInvoiceData();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InvoicePreviewScreen(
          invoiceData: invoiceData,
        ),
      ),
    ).then((result) {
      if (result == true && mounted) {
        // Invoice was sent, go back to main screen
        Navigator.pop(context, invoiceData);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4285F4),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.keyboard_arrow_down, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'New Invoice',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.arrow_forward, color: Colors.white),
            onPressed: () {
              // Navigate to next step or save
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildInvoiceHeader(),
            const SizedBox(height: 16),
            _buildLanguageSection(),
            const SizedBox(height: 16),
            _buildTemplatesSection(),
            const SizedBox(height: 16),
            _buildBusinessInfoSection(),
            const SizedBox(height: 16),
            _buildClientInfoSection(),
            const SizedBox(height: 16),
            _buildItemsSection(),
            const SizedBox(height: 16),
            _buildDiscountSection(),
            const SizedBox(height: 16),
            _buildTaxSection(),
            const SizedBox(height: 16),
            _buildShippingSection(),
            const SizedBox(height: 16),
            _buildTotalSection(),
            const SizedBox(height: 16),
            _buildCurrencySection(),
            const SizedBox(height: 16),
            _buildSignatureSection(),
            const SizedBox(height: 16),
            _buildTermsSection(),
            const SizedBox(height: 16),
            _buildPaymentMethodSection(),
            const SizedBox(height: 16),
            _buildAttachmentsSection(),
            const SizedBox(height: 16),
            _buildFeedbackSection(),
            const SizedBox(height: 100), // Space for buttons
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomButtons(),
    );
  }

  Widget _buildInvoiceHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(Icons.chevron_right, color: Colors.grey),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  invoiceNumber,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Created on ${DateFormat('yyyy/MM/dd').format(createdDate)}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                Text(
                  'Due on ${DateFormat('yyyy/MM/dd').format(dueDate)}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSection() {
    return _buildSectionCard(
      icon: Icons.language,
      title: '$language Invoice Language',
      onTap: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => LanguageSelectionScreen(
              currentLanguage: language,
            ),
          ),
        );
        if (result != null) {
          setState(() {
            language = result;
          });
        }
      },
    );
  }

  Widget _buildTemplatesSection() {
    return _buildSectionCard(
      icon: Icons.description_outlined,
      title: 'Templates',
      subtitle: template,
      onTap: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TemplateSelectionScreen(
              currentTemplate: template,
            ),
          ),
        );
        if (result != null) {
          setState(() {
            template = result;
          });
        }
      },
    );
  }

  Widget _buildBusinessInfoSection() {
    return _buildSectionCard(
      icon: Icons.business,
      title: 'Business Info',
      subtitle: businessInfo,
      onTap: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const BusinessInfoScreen(),
          ),
        );
        if (result != null) {
          setState(() {
            businessInfo = result['name'] ?? 'Add Business';
            businessData = result; // حفظ جميع البيانات
          });
        }
      },
    );
  }

  Widget _buildClientInfoSection() {
    return _buildSectionCard(
      icon: Icons.person_add,
      title: 'Client Info',
      subtitle: clientInfo,
      onTap: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const ClientInfoScreen(),
          ),
        );
        if (result != null) {
          setState(() {
            clientInfo = result['name'] ?? 'Add Client';
            clientData = result; // حفظ جميع البيانات
          });
        }
      },
    );
  }

  Widget _buildItemsSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(Icons.shopping_cart, color: Colors.grey),
                const SizedBox(width: 16),
                const Expanded(
                  child: Text(
                    'Items',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.add_circle, color: Color(0xFF4285F4)),
                  onPressed: () async {
                    final result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const AddItemScreen(),
                      ),
                    );
                    if (result != null) {
                      setState(() {
                        items.add(InvoiceItemData(
                          name: result['name'],
                          description: result['description'],
                          quantity: result['quantity'].toInt(),
                          unitPrice: result['unitPrice'],
                          total: result['total'],
                        ));
                        _calculateTotals();
                      });
                    }
                  },
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Text(
                  'Add Item',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () {
                    // Show options: Add from saved items or create new
                    showModalBottomSheet(
                      context: context,
                      builder: (context) => Container(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Text(
                              'إضافة عناصر',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 20),
                            ListTile(
                              leading: const Icon(Icons.inventory,
                                  color: Color(0xFF4285F4)),
                              title: const Text('اختيار من المنتجات المحفوظة'),
                              subtitle: const Text(
                                  'اختر من المنتجات والخدمات المحفوظة'),
                              onTap: () {
                                Navigator.pop(context);
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const SelectItemsScreen(),
                                  ),
                                ).then((result) {
                                  if (result != null && result is List) {
                                    setState(() {
                                      for (var item in result) {
                                        items.add(_convertToInvoiceItem(item));
                                      }
                                      _calculateTotals();
                                    });
                                  }
                                });
                              },
                            ),
                            ListTile(
                              leading: const Icon(Icons.add,
                                  color: Color(0xFF4285F4)),
                              title: const Text('إضافة عنصر جديد'),
                              subtitle: const Text('إنشاء عنصر جديد للفاتورة'),
                              onTap: () async {
                                Navigator.pop(context);
                                final result = await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const AddItemScreen(),
                                  ),
                                );
                                if (result != null) {
                                  setState(() {
                                    items.add(InvoiceItemData(
                                      name: result['name'],
                                      description: result['description'],
                                      quantity: result['quantity'].toInt(),
                                      unitPrice: result['unitPrice'],
                                      total: result['total'],
                                    ));
                                    _calculateTotals();
                                  });
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF4285F4),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          // Display added items
          if (items.isNotEmpty) ...[
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: items.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final item = items[index];
                return Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item.name,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            if (item.description.isNotEmpty) ...[
                              const SizedBox(height: 4),
                              Text(
                                item.description,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                            const SizedBox(height: 4),
                            Text(
                              '${item.quantity} × ${item.unitPrice.toStringAsFixed(2)}ج',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        '${item.total.toStringAsFixed(2)}ج',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete,
                            color: Colors.red, size: 20),
                        onPressed: () {
                          setState(() {
                            items.removeAt(index);
                            _calculateTotals();
                          });
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
            const Divider(height: 1),
          ],
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${subtotal.toStringAsFixed(2)}ج',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Text(
                  'Subtotal',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDiscountSection() {
    return _buildCalculationCard(
      icon: Icons.percent,
      title: 'Discount',
      amount: discount,
      percentage: discountPercent,
      onTap: () async {
        final result = await showDialog<Map<String, double>>(
          context: context,
          builder: (context) => CalculationDialog(
            title: 'Discount',
            type: 'discount',
            currentAmount: discount,
            currentPercentage: discountPercent,
            subtotal: subtotal,
          ),
        );
        if (result != null) {
          setState(() {
            discount = result['amount'] ?? 0.0;
            discountPercent = result['percentage'] ?? 0.0;
            _calculateTotals();
          });
        }
      },
    );
  }

  Widget _buildTaxSection() {
    return _buildCalculationCard(
      icon: Icons.account_balance,
      title: 'Tax',
      amount: tax,
      percentage: taxPercent,
      onTap: () async {
        final result = await showDialog<Map<String, double>>(
          context: context,
          builder: (context) => CalculationDialog(
            title: 'Tax',
            type: 'tax',
            currentAmount: tax,
            currentPercentage: taxPercent,
            subtotal: subtotal,
          ),
        );
        if (result != null) {
          setState(() {
            tax = result['amount'] ?? 0.0;
            taxPercent = result['percentage'] ?? 0.0;
            _calculateTotals();
          });
        }
      },
    );
  }

  Widget _buildShippingSection() {
    return _buildCalculationCard(
      icon: Icons.local_shipping,
      title: 'Shipping',
      amount: shipping,
      percentage: 0.0,
      showPercentage: false,
      onTap: () async {
        final result = await showDialog<Map<String, double>>(
          context: context,
          builder: (context) => CalculationDialog(
            title: 'Shipping',
            type: 'shipping',
            currentAmount: shipping,
            currentPercentage: 0.0,
            subtotal: subtotal,
          ),
        );
        if (result != null) {
          setState(() {
            shipping = result['amount'] ?? 0.0;
            _calculateTotals();
          });
        }
      },
    );
  }

  Widget _buildTotalSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF2C3E50),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '${total.toStringAsFixed(2)}ج',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const Text(
            'Total',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrencySection() {
    return _buildSectionCard(
      icon: Icons.attach_money,
      title: '$currency Currency',
      onTap: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CurrencySelectionScreen(
              currentCurrency: currency,
            ),
          ),
        );
        if (result != null) {
          setState(() {
            currency = result;
          });
        }
      },
    );
  }

  Widget _buildSignatureSection() {
    return _buildSectionCard(
      icon: Icons.edit,
      title: 'Signature',
      subtitle: signature,
      onTap: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SignatureScreen(
              currentSignature: signature,
            ),
          ),
        );
        if (result != null) {
          setState(() {
            signature = result;
          });
        }
      },
    );
  }

  Widget _buildTermsSection() {
    return _buildSectionCard(
      icon: Icons.description,
      title: 'Terms & Conditions',
      subtitle: termsConditions.isNotEmpty ? 'Added' : null,
      onTap: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TermsConditionsScreen(
              currentTerms: termsConditions,
            ),
          ),
        );
        if (result != null) {
          setState(() {
            termsConditions = result;
          });
        }
      },
    );
  }

  Widget _buildPaymentMethodSection() {
    return _buildSectionCard(
      icon: Icons.payment,
      title: 'Payment Method',
      subtitle: paymentMethod.isNotEmpty ? paymentMethod : null,
      onTap: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PaymentMethodScreen(
              currentPaymentMethod: paymentMethod,
            ),
          ),
        );
        if (result != null) {
          setState(() {
            paymentMethod = result;
          });
        }
      },
    );
  }

  Widget _buildAttachmentsSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(Icons.attach_file, color: Colors.grey),
                const SizedBox(width: 16),
                const Expanded(
                  child: Text(
                    'Attachments',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.attachment,
                    color: Colors.grey,
                    size: 16,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Text(
                  'Add Attachments',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF4285F4),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.add,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeedbackSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Column(
        children: [
          Text(
            'Tell us your suggestions about this page',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Give Feedback',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF4285F4),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                _saveInvoice();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4285F4),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Save',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                _previewInvoice();
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                side: const BorderSide(color: Colors.grey),
              ),
              child: const Text(
                'Preview',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Icon(Icons.chevron_right, color: Colors.grey),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (subtitle != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Icon(icon, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCalculationCard({
    required IconData icon,
    required String title,
    required double amount,
    required double percentage,
    bool showPercentage = true,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Icon(Icons.chevron_right, color: Colors.grey),
              const SizedBox(width: 16),
              Text(
                '${amount.toStringAsFixed(2)}ج',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (showPercentage) ...[
                Text(
                  '(${percentage.toStringAsFixed(0)}%)',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(width: 8),
              ],
              Icon(icon, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }
}

class InvoiceItemData {
  String name;
  String description;
  int quantity;
  double unitPrice;
  double total;

  InvoiceItemData({
    required this.name,
    required this.description,
    required this.quantity,
    required this.unitPrice,
    required this.total,
  });
}
