import 'package:flutter/material.dart';

class CurrencySelectionScreen extends StatefulWidget {
  final String currentCurrency;

  const CurrencySelectionScreen({
    super.key,
    required this.currentCurrency,
  });

  @override
  State<CurrencySelectionScreen> createState() => _CurrencySelectionScreenState();
}

class _CurrencySelectionScreenState extends State<CurrencySelectionScreen> {
  late String selectedCurrency;
  String searchQuery = '';

  final List<Map<String, String>> currencies = [
    {'code': 'SAR', 'name': 'Saudi Riyal', 'symbol': 'ج', 'country': 'Saudi Arabia'},
    {'code': 'USD', 'name': 'US Dollar', 'symbol': '\$', 'country': 'United States'},
    {'code': 'EUR', 'name': 'Euro', 'symbol': '€', 'country': 'European Union'},
    {'code': 'GBP', 'name': 'British Pound', 'symbol': '£', 'country': 'United Kingdom'},
    {'code': 'AED', 'name': 'UAE Dirham', 'symbol': 'د.إ', 'country': 'United Arab Emirates'},
    {'code': 'EGP', 'name': 'Egyptian Pound', 'symbol': 'ج.م', 'country': 'Egypt'},
    {'code': 'JPY', 'name': 'Japanese Yen', 'symbol': '¥', 'country': 'Japan'},
    {'code': 'CNY', 'name': 'Chinese Yuan', 'symbol': '¥', 'country': 'China'},
    {'code': 'INR', 'name': 'Indian Rupee', 'symbol': '₹', 'country': 'India'},
    {'code': 'CAD', 'name': 'Canadian Dollar', 'symbol': 'C\$', 'country': 'Canada'},
    {'code': 'AUD', 'name': 'Australian Dollar', 'symbol': 'A\$', 'country': 'Australia'},
    {'code': 'CHF', 'name': 'Swiss Franc', 'symbol': 'CHF', 'country': 'Switzerland'},
    {'code': 'SEK', 'name': 'Swedish Krona', 'symbol': 'kr', 'country': 'Sweden'},
    {'code': 'NOK', 'name': 'Norwegian Krone', 'symbol': 'kr', 'country': 'Norway'},
    {'code': 'DKK', 'name': 'Danish Krone', 'symbol': 'kr', 'country': 'Denmark'},
    {'code': 'PLN', 'name': 'Polish Zloty', 'symbol': 'zł', 'country': 'Poland'},
    {'code': 'RUB', 'name': 'Russian Ruble', 'symbol': '₽', 'country': 'Russia'},
    {'code': 'BRL', 'name': 'Brazilian Real', 'symbol': 'R\$', 'country': 'Brazil'},
    {'code': 'MXN', 'name': 'Mexican Peso', 'symbol': '\$', 'country': 'Mexico'},
    {'code': 'ZAR', 'name': 'South African Rand', 'symbol': 'R', 'country': 'South Africa'},
  ];

  @override
  void initState() {
    super.initState();
    selectedCurrency = widget.currentCurrency;
  }

  List<Map<String, String>> get filteredCurrencies {
    if (searchQuery.isEmpty) {
      return currencies;
    }
    return currencies.where((currency) {
      return currency['name']!.toLowerCase().contains(searchQuery.toLowerCase()) ||
             currency['code']!.toLowerCase().contains(searchQuery.toLowerCase()) ||
             currency['country']!.toLowerCase().contains(searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4285F4),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Select Currency',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context, selectedCurrency);
            },
            child: const Text(
              'Done',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search currencies...',
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF4285F4)),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
            ),
          ),
          
          // Currency list
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ListView.separated(
                padding: const EdgeInsets.all(8),
                itemCount: filteredCurrencies.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final currency = filteredCurrencies[index];
                  final isSelected = selectedCurrency.contains(currency['symbol']!);
                  
                  return ListTile(
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    leading: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: isSelected 
                            ? const Color(0xFF4285F4).withValues(alpha: 0.1)
                            : Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: Center(
                        child: Text(
                          currency['symbol']!,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: isSelected 
                                ? const Color(0xFF4285F4)
                                : Colors.grey.shade600,
                          ),
                        ),
                      ),
                    ),
                    title: Text(
                      currency['name']!,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        color: isSelected ? const Color(0xFF4285F4) : Colors.black,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          currency['code']!,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: isSelected 
                                ? const Color(0xFF4285F4).withValues(alpha: 0.7)
                                : Colors.grey.shade600,
                          ),
                        ),
                        Text(
                          currency['country']!,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                    trailing: isSelected
                        ? const Icon(
                            Icons.check_circle,
                            color: Color(0xFF4285F4),
                          )
                        : const Icon(
                            Icons.radio_button_unchecked,
                            color: Colors.grey,
                          ),
                    onTap: () {
                      setState(() {
                        selectedCurrency = '${currency['code']} ${currency['symbol']}';
                      });
                    },
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
