import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:invoice/features/invoice/domain/entities/invoice.dart';
import 'package:invoice/core/database/database_helper.dart';
import 'package:invoice/features/invoice/presentation/screens/select_items_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/business_info_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/client_info_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/add_item_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/calculation_dialog.dart';
import 'package:invoice/features/invoice/presentation/screens/language_selection_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/template_selection_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/currency_selection_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/signature_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/terms_conditions_screen.dart';
import 'package:invoice/features/invoice/presentation/screens/payment_method_screen.dart';

class InvoiceItemData {
  final String name;
  final String description;
  final int quantity;
  final double unitPrice;
  final double total;

  InvoiceItemData({
    required this.name,
    required this.description,
    required this.quantity,
    required this.unitPrice,
    required this.total,
  });
}

class EditInvoiceScreen extends StatefulWidget {
  final Invoice invoice;

  const EditInvoiceScreen({
    super.key,
    required this.invoice,
  });

  @override
  State<EditInvoiceScreen> createState() => _EditInvoiceScreenState();
}

class _EditInvoiceScreenState extends State<EditInvoiceScreen> {
  final ScrollController _scrollController = ScrollController();

  // Form data - initialized from existing invoice
  late String invoiceNumber;
  late DateTime createdDate;
  late DateTime dueDate;
  late String language;
  late String template;
  late String businessInfo;
  late String clientInfo;
  late Map<String, dynamic>? businessData;
  late Map<String, dynamic>? clientData;
  late List<InvoiceItemData> items;
  late double subtotal;
  late double discount;
  late double discountPercent;
  late double tax;
  late double taxPercent;
  late double shipping;
  late double total;
  late String currency;
  late String signature;
  late String termsConditions;
  late String paymentMethod;
  late String status;

  @override
  void initState() {
    super.initState();
    _initializeFromInvoice();
  }

  void _initializeFromInvoice() {
    final invoice = widget.invoice;

    invoiceNumber = invoice.invoiceNumber;
    createdDate = invoice.creationDate;
    dueDate = invoice.dueDate;
    language = invoice.language;
    template = invoice.template;
    status = invoice.status;

    // Business info
    businessInfo = invoice.businessName;
    businessData = {
      'name': invoice.businessName,
      'email': invoice.businessEmail,
      'phone': invoice.businessPhone,
      'address': invoice.businessAddress,
      'website': invoice.businessWebsite ?? '',
      'taxNumber': invoice.businessTaxId ?? '',
      'company': invoice.businessCompany ?? '',
    };

    // Client info
    clientInfo = invoice.clientName;
    clientData = {
      'name': invoice.clientName,
      'email': invoice.clientEmail,
      'phone': invoice.clientPhone,
      'address': invoice.clientAddress,
      'company': invoice.clientCompany ?? '',
      'taxNumber': invoice.clientTaxId ?? '',
    };

    // Items
    items = invoice.items
        .map((item) => InvoiceItemData(
              name: item.name,
              description: item.description ?? '',
              quantity: item.quantity.toInt(),
              unitPrice: item.unitPrice,
              total: item.amount,
            ))
        .toList();

    // Calculations
    subtotal = invoice.subtotal;
    discount = invoice.discount;
    tax = invoice.tax;
    shipping = invoice.shipping;
    total = invoice.total;
    currency = '${invoice.currency} ج';

    // Calculate percentages
    if (subtotal > 0) {
      discountPercent = (discount / subtotal) * 100;
      taxPercent = (tax / subtotal) * 100;
    } else {
      discountPercent = 0.0;
      taxPercent = 0.0;
    }

    // Other fields
    signature = invoice.signature ?? 'Add Signature';
    termsConditions = invoice.terms ?? '';
    paymentMethod = invoice.paymentMethod ?? '';
  }

  void _calculateTotals() {
    subtotal = items.fold(0.0, (sum, item) => sum + item.total);

    // Calculate tax based on current tax percentage
    tax = subtotal * taxPercent / 100;

    // Calculate discount based on current discount percentage
    discount = subtotal * discountPercent / 100;

    total = subtotal + tax + shipping - discount;
  }

  Future<void> _updateInvoice() async {
    if (items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add at least one item to the invoice'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      print('🔄 Updating invoice: ${widget.invoice.id}');

      final db = await DatabaseHelper.instance.database;
      final now = DateTime.now();

      // Update invoice in database
      final invoiceData = {
        'invoice_number': invoiceNumber,
        'creation_date': createdDate.toIso8601String(),
        'due_date': dueDate.toIso8601String(),
        'business_name': businessData?['name'] ?? businessInfo,
        'business_email': businessData?['email'] ?? '',
        'business_phone': businessData?['phone'] ?? '',
        'business_address': businessData?['address'] ?? '',
        'business_website': businessData?['website'] ?? '',
        'business_tax_id': businessData?['taxNumber'] ?? '',
        'business_company': businessData?['company'] ?? '',
        'client_name': clientData?['name'] ?? clientInfo,
        'client_email': clientData?['email'] ?? '',
        'client_phone': clientData?['phone'] ?? '',
        'client_address': clientData?['address'] ?? '',
        'client_company': clientData?['company'] ?? '',
        'client_tax_id': clientData?['taxNumber'] ?? '',
        'subtotal': subtotal,
        'discount': discount,
        'tax': tax,
        'shipping': shipping,
        'total': total,
        'currency': currency.replaceAll(' ج', ''),
        'language': language,
        'template': template,
        'status': status,
        'terms': termsConditions,
        'signature': signature != 'Add Signature' ? signature : '',
        'payment_method': paymentMethod,
        'updated_at': now.toIso8601String(),
      };

      await db.update(
        'invoices',
        invoiceData,
        where: 'id = ?',
        whereArgs: [widget.invoice.id],
      );

      print('✅ Invoice updated in database');

      // Delete existing items and insert new ones
      await db.delete(
        'invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [widget.invoice.id],
      );

      // Insert updated items
      for (final item in items) {
        final itemData = {
          'id': '${widget.invoice.id}_${items.indexOf(item)}',
          'invoice_id': widget.invoice.id,
          'name': item.name,
          'description': item.description,
          'quantity': item.quantity.toDouble(),
          'unit_price': item.unitPrice,
          'amount': item.total,
          'notes': '',
          'created_at': now.toIso8601String(),
        };
        await db.insert('invoice_items', itemData);
      }

      print('✅ Invoice items updated in database');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Invoice ${invoiceNumber} updated successfully!'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
        Navigator.pop(
            context, true); // Return true to indicate successful update
      }
    } catch (e) {
      print('❌ Error updating invoice: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating invoice: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4285F4),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Edit Invoice ${invoiceNumber}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save, color: Colors.white),
            onPressed: _updateInvoice,
          ),
        ],
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildInvoiceHeader(),
            const SizedBox(height: 16),
            _buildLanguageSection(),
            const SizedBox(height: 16),
            _buildTemplatesSection(),
            const SizedBox(height: 16),
            _buildBusinessInfoSection(),
            const SizedBox(height: 16),
            _buildClientInfoSection(),
            const SizedBox(height: 16),
            _buildItemsSection(),
            const SizedBox(height: 16),
            _buildCalculationSection(),
            const SizedBox(height: 16),
            _buildSignatureSection(),
            const SizedBox(height: 16),
            _buildTermsSection(),
            const SizedBox(height: 16),
            _buildPaymentMethodSection(),
            const SizedBox(height: 16),
            _buildStatusSection(),
            const SizedBox(height: 100), // Space for bottom actions
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomActions(),
    );
  }

  Widget _buildInvoiceHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Invoice Number',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      initialValue: invoiceNumber,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      onChanged: (value) {
                        setState(() {
                          invoiceNumber = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Status',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: status,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: ['paid', 'unpaid', 'overdue', 'draft']
                          .map((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value.toUpperCase()),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            status = value;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Creation Date',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: createdDate,
                          firstDate: DateTime(2020),
                          lastDate: DateTime(2030),
                        );
                        if (date != null) {
                          setState(() {
                            createdDate = date;
                          });
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('dd/MM/yyyy').format(createdDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Due Date',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: dueDate,
                          firstDate: DateTime(2020),
                          lastDate: DateTime(2030),
                        );
                        if (date != null) {
                          setState(() {
                            dueDate = date;
                          });
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('dd/MM/yyyy').format(dueDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSection() {
    return _buildSectionCard(
      title: 'Language',
      child: ListTile(
        leading: const Icon(Icons.language, color: Color(0xFF4285F4)),
        title: Text(language),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  LanguageSelectionScreen(currentLanguage: language),
            ),
          );
          if (result != null) {
            setState(() {
              language = result;
            });
          }
        },
      ),
    );
  }

  Widget _buildTemplatesSection() {
    return _buildSectionCard(
      title: 'Template',
      child: ListTile(
        leading: const Icon(Icons.design_services, color: Color(0xFF4285F4)),
        title: Text(template),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  TemplateSelectionScreen(currentTemplate: template),
            ),
          );
          if (result != null) {
            setState(() {
              template = result;
            });
          }
        },
      ),
    );
  }

  Widget _buildSectionCard({required String title, required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
          ),
          child,
        ],
      ),
    );
  }

  Widget _buildBusinessInfoSection() {
    return _buildSectionCard(
      title: 'Business Information',
      child: ListTile(
        leading: const Icon(Icons.business, color: Color(0xFF4285F4)),
        title: Text(businessInfo),
        subtitle:
            businessData != null ? Text(businessData!['email'] ?? '') : null,
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const BusinessInfoScreen(),
            ),
          );
          if (result != null) {
            setState(() {
              businessData = result;
              businessInfo = result['name'] ?? 'Add Business';
            });
          }
        },
      ),
    );
  }

  Widget _buildClientInfoSection() {
    return _buildSectionCard(
      title: 'Client Information',
      child: ListTile(
        leading: const Icon(Icons.person, color: Color(0xFF4285F4)),
        title: Text(clientInfo),
        subtitle: clientData != null ? Text(clientData!['email'] ?? '') : null,
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ClientInfoScreen(),
            ),
          );
          if (result != null) {
            setState(() {
              clientData = result;
              clientInfo = result['name'] ?? 'Add Client';
            });
          }
        },
      ),
    );
  }

  Widget _buildItemsSection() {
    return _buildSectionCard(
      title: 'Items (${items.length})',
      child: Column(
        children: [
          ...items.map((item) => _buildItemTile(item)).toList(),
          ListTile(
            leading: const Icon(Icons.add, color: Color(0xFF4285F4)),
            title: const Text('Add Item'),
            onTap: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddItemScreen(),
                ),
              );
              if (result != null) {
                setState(() {
                  items.add(result);
                  _calculateTotals();
                });
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildItemTile(InvoiceItemData item) {
    return ListTile(
      leading: const Icon(Icons.inventory_2, color: Colors.grey),
      title: Text(item.name),
      subtitle:
          Text('Qty: ${item.quantity} × ${item.unitPrice.toStringAsFixed(2)}'),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '${item.total.toStringAsFixed(2)} SAR',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          IconButton(
            icon: const Icon(Icons.delete, color: Colors.red),
            onPressed: () {
              setState(() {
                items.remove(item);
                _calculateTotals();
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCalculationSection() {
    return _buildSectionCard(
      title: 'Calculations',
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.calculate, color: Color(0xFF4285F4)),
            title: const Text('Subtotal'),
            trailing: Text('${subtotal.toStringAsFixed(2)} SAR'),
          ),
          ListTile(
            leading: const Icon(Icons.percent, color: Colors.orange),
            title: Text('Tax (${taxPercent.toStringAsFixed(1)}%)'),
            trailing: Text('${tax.toStringAsFixed(2)} SAR'),
            onTap: () async {
              final result = await showDialog<Map<String, double>>(
                context: context,
                builder: (context) => CalculationDialog(
                  title: 'Tax',
                  type: 'tax',
                  currentPercentage: taxPercent,
                  currentAmount: tax,
                  subtotal: subtotal,
                ),
              );
              if (result != null) {
                setState(() {
                  taxPercent = result['percent'] ?? 0.0;
                  tax = result['amount'] ?? 0.0;
                  _calculateTotals();
                });
              }
            },
          ),
          ListTile(
            leading: const Icon(Icons.local_shipping, color: Colors.green),
            title: const Text('Shipping'),
            trailing: Text('${shipping.toStringAsFixed(2)} SAR'),
            onTap: () async {
              final controller =
                  TextEditingController(text: shipping.toString());
              final result = await showDialog<double>(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Shipping Cost'),
                  content: TextField(
                    controller: controller,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Amount',
                      suffixText: 'SAR',
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        final value = double.tryParse(controller.text) ?? 0.0;
                        Navigator.pop(context, value);
                      },
                      child: const Text('Save'),
                    ),
                  ],
                ),
              );
              if (result != null) {
                setState(() {
                  shipping = result;
                  _calculateTotals();
                });
              }
            },
          ),
          const Divider(),
          ListTile(
            leading:
                const Icon(Icons.monetization_on, color: Color(0xFF4285F4)),
            title: const Text('Total',
                style: TextStyle(fontWeight: FontWeight.bold)),
            trailing: Text(
              '${total.toStringAsFixed(2)} SAR',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSignatureSection() {
    return _buildSectionCard(
      title: 'Signature',
      child: ListTile(
        leading: const Icon(Icons.draw, color: Color(0xFF4285F4)),
        title: Text(signature),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  SignatureScreen(currentSignature: signature),
            ),
          );
          if (result != null) {
            setState(() {
              signature = result;
            });
          }
        },
      ),
    );
  }

  Widget _buildTermsSection() {
    return _buildSectionCard(
      title: 'Terms & Conditions',
      child: ListTile(
        leading: const Icon(Icons.description, color: Color(0xFF4285F4)),
        title: Text(termsConditions.isEmpty ? 'Add Terms' : 'Terms Added'),
        subtitle: termsConditions.isNotEmpty
            ? Text(termsConditions.length > 50
                ? '${termsConditions.substring(0, 50)}...'
                : termsConditions)
            : null,
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  TermsConditionsScreen(currentTerms: termsConditions),
            ),
          );
          if (result != null) {
            setState(() {
              termsConditions = result;
            });
          }
        },
      ),
    );
  }

  Widget _buildPaymentMethodSection() {
    return _buildSectionCard(
      title: 'Payment Method',
      child: ListTile(
        leading: const Icon(Icons.payment, color: Color(0xFF4285F4)),
        title:
            Text(paymentMethod.isEmpty ? 'Add Payment Method' : paymentMethod),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  PaymentMethodScreen(currentPaymentMethod: paymentMethod),
            ),
          );
          if (result != null) {
            setState(() {
              paymentMethod = result;
            });
          }
        },
      ),
    );
  }

  Widget _buildStatusSection() {
    return _buildSectionCard(
      title: 'Invoice Status',
      child: Column(
        children: [
          RadioListTile<String>(
            title: const Text('Paid'),
            value: 'paid',
            groupValue: status,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  status = value;
                });
              }
            },
          ),
          RadioListTile<String>(
            title: const Text('Unpaid'),
            value: 'unpaid',
            groupValue: status,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  status = value;
                });
              }
            },
          ),
          RadioListTile<String>(
            title: const Text('Overdue'),
            value: 'overdue',
            groupValue: status,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  status = value;
                });
              }
            },
          ),
          RadioListTile<String>(
            title: const Text('Draft'),
            value: 'draft',
            groupValue: status,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  status = value;
                });
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.cancel),
              label: const Text('Cancel'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: _updateInvoice,
              icon: const Icon(Icons.save, color: Colors.white),
              label: const Text('Update Invoice',
                  style: TextStyle(color: Colors.white)),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4285F4),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
