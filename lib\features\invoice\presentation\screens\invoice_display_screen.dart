import 'package:flutter/material.dart';
import 'package:invoice/features/invoice/domain/entities/invoice.dart';
import 'package:invoice/features/invoice/presentation/widgets/invoice_templates.dart';
import 'package:invoice/core/database/database_helper.dart';

class InvoiceDisplayScreen extends StatefulWidget {
  final String invoiceId;

  const InvoiceDisplayScreen({
    super.key,
    required this.invoiceId,
  });

  @override
  State<InvoiceDisplayScreen> createState() => _InvoiceDisplayScreenState();
}

class _InvoiceDisplayScreenState extends State<InvoiceDisplayScreen> {
  Invoice? invoice;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadInvoiceData();
  }

  Future<void> _loadInvoiceData() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final db = await DatabaseHelper.instance.database;

      // Load invoice data
      final invoiceData = await db.query(
        'invoices',
        where: 'id = ?',
        whereArgs: [widget.invoiceId],
        limit: 1,
      );

      if (invoiceData.isEmpty) {
        throw Exception('Invoice not found');
      }

      final invoiceRecord = invoiceData.first;

      // Load invoice items
      final itemsData = await db.query(
        'invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [widget.invoiceId],
      );

      final items = itemsData
          .map((itemData) => InvoiceItem(
                id: itemData['id'] as String,
                name: itemData['name'] as String,
                description: itemData['description'] as String? ?? '',
                quantity: (itemData['quantity'] as num).toDouble(),
                unitPrice: (itemData['unit_price'] as num).toDouble(),
                amount: (itemData['amount'] as num).toDouble(),
                createdAt: DateTime.parse(itemData['created_at'] as String),
              ))
          .toList();

      // Create Invoice object
      final loadedInvoice = Invoice(
        id: invoiceRecord['id'] as String,
        invoiceNumber: invoiceRecord['invoice_number'] as String,
        creationDate: DateTime.parse(invoiceRecord['creation_date'] as String),
        dueDate: DateTime.parse(invoiceRecord['due_date'] as String),
        dueTerms: invoiceRecord['due_terms'] as String? ?? '7 days',
        invoiceTitle: invoiceRecord['invoice_title'] as String? ?? 'INVOICE',
        businessName: invoiceRecord['business_name'] as String,
        businessEmail: invoiceRecord['business_email'] as String,
        businessPhone: invoiceRecord['business_phone'] as String,
        businessAddress: invoiceRecord['business_address'] as String,
        businessWebsite: invoiceRecord['business_website'] as String?,
        businessTaxId: invoiceRecord['business_tax_id'] as String?,
        businessCompany: invoiceRecord['business_company'] as String?,
        clientName: invoiceRecord['client_name'] as String,
        clientEmail: invoiceRecord['client_email'] as String,
        clientPhone: invoiceRecord['client_phone'] as String,
        clientAddress: invoiceRecord['client_address'] as String,
        clientCompany: invoiceRecord['client_company'] as String?,
        clientTaxId: invoiceRecord['client_tax_id'] as String?,
        items: items,
        subtotal: (invoiceRecord['subtotal'] as num).toDouble(),
        discount: (invoiceRecord['discount'] as num).toDouble(),
        discountType: invoiceRecord['discount_type'] as String? ?? 'percentage',
        tax: (invoiceRecord['tax'] as num).toDouble(),
        taxType: invoiceRecord['tax_type'] as String? ?? 'percentage',
        shipping: (invoiceRecord['shipping'] as num).toDouble(),
        total: (invoiceRecord['total'] as num).toDouble(),
        currency: invoiceRecord['currency'] as String,
        language: invoiceRecord['language'] as String? ?? 'Arabic',
        template: invoiceRecord['template'] as String? ?? 'Modern',
        status: invoiceRecord['status'] as String? ?? 'unpaid',
        terms: invoiceRecord['terms'] as String?,
        signature: invoiceRecord['signature'] as String?,
        paymentMethod: invoiceRecord['payment_method'] as String?,
        createdAt: DateTime.parse(invoiceRecord['created_at'] as String),
        updatedAt: DateTime.parse(invoiceRecord['updated_at'] as String),
      );

      if (mounted) {
        setState(() {
          invoice = loadedInvoice;
          isLoading = false;
        });
        print('✅ Loaded invoice: ${loadedInvoice.invoiceNumber}');
      }
    } catch (e) {
      print('❌ Error loading invoice: $e');
      if (mounted) {
        setState(() {
          errorMessage = e.toString();
          isLoading = false;
        });
      }
    }
  }

  Map<String, dynamic> _convertInvoiceToDisplayData(Invoice invoice) {
    return {
      'invoiceNumber': invoice.invoiceNumber,
      'createdDate': invoice.creationDate,
      'dueDate': invoice.dueDate,
      'template': invoice.template,
      'businessInfo': {
        'name': invoice.businessName,
        'email': invoice.businessEmail,
        'phone': invoice.businessPhone,
        'address': invoice.businessAddress,
        'website': invoice.businessWebsite,
        'taxId': invoice.businessTaxId,
        'company': invoice.businessCompany,
      },
      'clientInfo': {
        'name': invoice.clientName,
        'email': invoice.clientEmail,
        'phone': invoice.clientPhone,
        'address': invoice.clientAddress,
        'company': invoice.clientCompany,
        'taxId': invoice.clientTaxId,
      },
      'items': invoice.items
          .map((item) => {
                'name': item.name,
                'description': item.description,
                'quantity': item.quantity.toInt(),
                'unitPrice': item.unitPrice,
                'total': item.amount,
              })
          .toList(),
      'subtotal': invoice.subtotal,
      'discount': invoice.discount,
      'tax': invoice.tax,
      'shipping': invoice.shipping,
      'total': invoice.total,
      'currency': invoice.currency,
      'termsConditions': invoice.terms ?? '',
      'signature': invoice.signature ?? '',
      'paymentMethod': invoice.paymentMethod ?? '',
      'status': invoice.status,
      'language': invoice.language,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4285F4),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          invoice?.invoiceNumber ?? 'Invoice',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (invoice != null) ...[
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.white),
              onPressed: () {
                // TODO: Navigate to edit invoice screen
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Edit functionality coming soon'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
            ),
            IconButton(
              icon: const Icon(Icons.share, color: Colors.white),
              onPressed: () {
                // TODO: Share invoice functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Share functionality coming soon'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
            ),
            IconButton(
              icon: const Icon(Icons.download, color: Colors.white),
              onPressed: () {
                // TODO: Download/Export invoice functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Download functionality coming soon'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
            ),
          ],
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: invoice != null ? _buildBottomActions() : null,
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading invoice...'),
          ],
        ),
      );
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading invoice',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.red.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.red.shade500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadInvoiceData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (invoice == null) {
      return const Center(
        child: Text('Invoice not found'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: InvoiceTemplates.buildInvoice(
            invoice!.template ?? 'Modern',
            _convertInvoiceToDisplayData(invoice!),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {
                // TODO: Mark as paid/unpaid functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Payment status update coming soon'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
              icon: Icon(
                invoice!.status.toLowerCase() == 'paid'
                    ? Icons.money_off
                    : Icons.payment,
              ),
              label: Text(
                invoice!.status.toLowerCase() == 'paid'
                    ? 'Mark Unpaid'
                    : 'Mark Paid',
              ),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                // TODO: Send invoice functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Send functionality coming soon'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
              icon: const Icon(Icons.send),
              label: const Text('Send Invoice'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4285F4),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
