import 'package:flutter/material.dart';
import 'package:invoice/features/invoice/domain/entities/invoice.dart';
import 'package:invoice/features/invoice/presentation/widgets/invoice_list_item.dart';
import 'package:invoice/features/invoice/presentation/screens/create_invoice_screen.dart';
// Navigation imports removed - now handled by MainNavigationWrapper
import 'package:invoice/features/invoice/presentation/screens/invoice_display_screen.dart';
import 'package:invoice/core/database/database_helper.dart';
import 'package:invoice/debug/database_test_screen.dart';
// Settings import removed - now handled by MainNavigationWrapper
import 'package:invoice/test_settings.dart';

class InvoiceListScreen extends StatefulWidget {
  const InvoiceListScreen({super.key});

  @override
  State<InvoiceListScreen> createState() => _InvoiceListScreenState();
}

class _InvoiceListScreenState extends State<InvoiceListScreen> {
  String selectedFilter = 'All';
  // Bottom navigation is now handled by MainNavigationWrapper
  List<Invoice> invoices = [];
  bool isLoading = true;

  // Statistics
  double totalOverdue = 0.0;
  double totalUnpaid = 0.0;
  int overdueCount = 0;
  int unpaidCount = 0;
  String defaultCurrency = 'SAR';

  @override
  void initState() {
    super.initState();
    _loadInvoices();
  }

  Future<void> _loadInvoices() async {
    try {
      final db = await DatabaseHelper.instance.database;

      // Load invoices from database
      final invoicesData = await db.query(
        'invoices',
        orderBy: 'created_at DESC',
      );

      // Convert to Invoice objects
      final loadedInvoices = <Invoice>[];

      for (final invoiceData in invoicesData) {
        // Load invoice items
        final itemsData = await db.query(
          'invoice_items',
          where: 'invoice_id = ?',
          whereArgs: [invoiceData['id']],
        );

        final items = itemsData
            .map((itemData) => InvoiceItem(
                  id: itemData['id'] as String,
                  name: itemData['name'] as String,
                  description: itemData['description'] as String? ?? '',
                  quantity: (itemData['quantity'] as num).toDouble(),
                  unitPrice: (itemData['unit_price'] as num).toDouble(),
                  amount: (itemData['amount'] as num).toDouble(),
                  createdAt: DateTime.parse(itemData['created_at'] as String),
                ))
            .toList();

        final invoice = Invoice(
          id: invoiceData['id'] as String,
          invoiceNumber: invoiceData['invoice_number'] as String,
          creationDate: DateTime.parse(invoiceData['creation_date'] as String),
          dueDate: DateTime.parse(invoiceData['due_date'] as String),
          dueTerms: invoiceData['due_terms'] as String? ?? '7 days',
          invoiceTitle: invoiceData['invoice_title'] as String? ?? 'INVOICE',
          businessName: invoiceData['business_name'] as String,
          businessEmail: invoiceData['business_email'] as String,
          businessPhone: invoiceData['business_phone'] as String,
          businessAddress: invoiceData['business_address'] as String,
          businessWebsite: invoiceData['business_website'] as String?,
          businessTaxId: invoiceData['business_tax_id'] as String?,
          businessCompany: invoiceData['business_company'] as String?,
          clientName: invoiceData['client_name'] as String,
          clientEmail: invoiceData['client_email'] as String,
          clientPhone: invoiceData['client_phone'] as String,
          clientAddress: invoiceData['client_address'] as String,
          clientCompany: invoiceData['client_company'] as String?,
          clientTaxId: invoiceData['client_tax_id'] as String?,
          items: items,
          subtotal: (invoiceData['subtotal'] as num).toDouble(),
          discount: (invoiceData['discount'] as num).toDouble(),
          discountType: invoiceData['discount_type'] as String? ?? 'percentage',
          tax: (invoiceData['tax'] as num).toDouble(),
          taxType: invoiceData['tax_type'] as String? ?? 'percentage',
          shipping: (invoiceData['shipping'] as num).toDouble(),
          total: (invoiceData['total'] as num).toDouble(),
          currency: invoiceData['currency'] as String,
          language: invoiceData['language'] as String? ?? 'Arabic',
          template: invoiceData['template'] as String? ?? 'Modern',
          status: invoiceData['status'] as String? ?? 'unpaid',
          terms: invoiceData['terms'] as String?,
          signature: invoiceData['signature'] as String?,
          paymentMethod: invoiceData['payment_method'] as String?,
          createdAt: DateTime.parse(invoiceData['created_at'] as String),
          updatedAt: DateTime.parse(invoiceData['updated_at'] as String),
        );

        loadedInvoices.add(invoice);
      }

      // Calculate statistics
      _calculateStatistics(loadedInvoices);

      if (mounted) {
        setState(() {
          invoices = loadedInvoices;
          isLoading = false;
        });
        debugPrint('✅ Loaded ${loadedInvoices.length} invoices from database');
      }
    } catch (e) {
      debugPrint('❌ Error loading invoices: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الفواتير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _calculateStatistics(List<Invoice> invoices) {
    totalOverdue = 0.0;
    totalUnpaid = 0.0;
    overdueCount = 0;
    unpaidCount = 0;

    final now = DateTime.now();

    for (final invoice in invoices) {
      if (invoice.status.toLowerCase() == 'unpaid') {
        totalUnpaid += invoice.total;
        unpaidCount++;

        // Check if overdue
        if (invoice.dueDate.isBefore(now)) {
          totalOverdue += invoice.total;
          overdueCount++;
        }
      }

      // Set default currency from first invoice
      if (invoices.isNotEmpty) {
        defaultCurrency = invoices.first.currency;
      }
    }
  }

  List<Invoice> _getFilteredInvoices(List<Invoice> invoices) {
    final now = DateTime.now();

    switch (selectedFilter) {
      case 'Overdue':
        return invoices
            .where((invoice) =>
                invoice.status.toLowerCase() == 'unpaid' &&
                invoice.dueDate.isBefore(now))
            .toList();
      case 'Partially Paid':
        return invoices
            .where(
                (invoice) => invoice.status.toLowerCase() == 'partially_paid')
            .toList();
      case 'Unpaid':
        return invoices
            .where((invoice) => invoice.status.toLowerCase() == 'unpaid')
            .toList();
      case 'All':
      default:
        return invoices;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4285F4),
        elevation: 0,
        automaticallyImplyLeading:
            false, // Remove back button for persistent navigation
        title: const Text(
          'Invoice',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.settings_applications, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TestSettingsScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.storage, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DatabaseTestScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onPressed: () {},
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(invoices),
      floatingActionButton: FloatingActionButton(
        backgroundColor: const Color(0xFF4285F4),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateInvoiceScreen(),
            ),
          ).then((result) {
            if (result != null && mounted) {
              // Reload invoices after creating new one
              _loadInvoices();
            }
          });
        },
        child: const Icon(Icons.add, color: Colors.white),
      ),
      // Bottom navigation bar is now handled by MainNavigationWrapper
    );
  }

  Widget _buildContent(List<Invoice> invoices) {
    return Column(
      children: [
        _buildStatsCards(),
        _buildFilterTabs(),
        Expanded(
          child: _buildInvoiceList(_getFilteredInvoices(invoices)),
        ),
      ],
    );
  }

  Widget _buildStatsCards() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              title: 'Total Overdue ($overdueCount)',
              amount: totalOverdue.toStringAsFixed(2),
              currency: defaultCurrency,
              color: Colors.red,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              title: 'Total Unpaid ($unpaidCount)',
              amount: totalUnpaid.toStringAsFixed(2),
              currency: defaultCurrency,
              color: Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String amount,
    required String currency,
    Color? color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '$amount $currency',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color ?? Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs() {
    final filters = ['Overdue', 'Partially Paid', 'Unpaid', 'All'];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          const Icon(Icons.filter_list, color: Colors.grey),
          const SizedBox(width: 16),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: filters.map((filter) {
                  final isSelected = selectedFilter == filter;
                  return Padding(
                    padding: const EdgeInsets.only(right: 12),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          selectedFilter = filter;
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? const Color(0xFF4285F4)
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isSelected
                                ? const Color(0xFF4285F4)
                                : Colors.grey.shade300,
                          ),
                        ),
                        child: Text(
                          filter,
                          style: TextStyle(
                            color: isSelected
                                ? Colors.white
                                : Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceList(List<Invoice> invoices) {
    // Show empty state if no invoices
    if (invoices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No invoices yet',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Tap the + button to create your first invoice',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: invoices.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final invoice = invoices[index];
        return InvoiceListItem(
          invoice: invoice,
          onTap: () {
            // Navigate to invoice display screen
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => InvoiceDisplayScreen(
                  invoiceId: invoice.id,
                ),
              ),
            );
          },
        );
      },
    );
  }

  // Bottom navigation bar is now handled by MainNavigationWrapper
}
