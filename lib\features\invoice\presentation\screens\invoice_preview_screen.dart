import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class InvoicePreviewScreen extends StatelessWidget {
  final Map<String, dynamic> invoiceData;

  const InvoicePreviewScreen({
    super.key,
    required this.invoiceData,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4285F4),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Invoice Preview',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: () {
              // Share invoice
            },
          ),
          IconButton(
            icon: const Icon(Icons.download, color: Colors.white),
            onPressed: () {
              // Download invoice
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 32),
                _buildBusinessInfo(),
                const SizedBox(height: 24),
                _buildClientInfo(),
                const SizedBox(height: 32),
                _buildItemsTable(),
                const SizedBox(height: 24),
                _buildTotals(),
                const SizedBox(height: 32),
                _buildFooter(),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomActions(context),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'INVOICE',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4285F4),
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  invoiceData['invoiceNumber'] ?? 'INV00001',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Date: ${DateFormat('dd/MM/yyyy').format(invoiceData['createdDate'] ?? DateTime.now())}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                Text(
                  'Due: ${DateFormat('dd/MM/yyyy').format(invoiceData['dueDate'] ?? DateTime.now())}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBusinessInfo() {
    final businessInfo = invoiceData['businessInfo'] as Map<String, dynamic>?;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'From:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          businessInfo?['name'] ?? 'Your Business Name',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        if (businessInfo?['email'] != null) ...[
          const SizedBox(height: 4),
          Text(
            businessInfo!['email'],
            style: const TextStyle(fontSize: 14),
          ),
        ],
        if (businessInfo?['phone'] != null) ...[
          const SizedBox(height: 4),
          Text(
            businessInfo!['phone'],
            style: const TextStyle(fontSize: 14),
          ),
        ],
        if (businessInfo?['address'] != null) ...[
          const SizedBox(height: 4),
          Text(
            businessInfo!['address'],
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ],
    );
  }

  Widget _buildClientInfo() {
    final clientInfo = invoiceData['clientInfo'] as Map<String, dynamic>?;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'To:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          clientInfo?['name'] ?? 'Client Name',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        if (clientInfo?['company'] != null && clientInfo!['company'].isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            clientInfo['company'],
            style: const TextStyle(fontSize: 14),
          ),
        ],
        if (clientInfo?['email'] != null) ...[
          const SizedBox(height: 4),
          Text(
            clientInfo!['email'],
            style: const TextStyle(fontSize: 14),
          ),
        ],
        if (clientInfo?['phone'] != null) ...[
          const SizedBox(height: 4),
          Text(
            clientInfo!['phone'],
            style: const TextStyle(fontSize: 14),
          ),
        ],
        if (clientInfo?['address'] != null) ...[
          const SizedBox(height: 4),
          Text(
            clientInfo!['address'],
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ],
    );
  }

  Widget _buildItemsTable() {
    final items = invoiceData['items'] as List<dynamic>? ?? [];
    
    return Column(
      children: [
        // Table header
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: const Row(
            children: [
              Expanded(flex: 3, child: Text('Item', style: TextStyle(fontWeight: FontWeight.bold))),
              Expanded(flex: 1, child: Text('Qty', style: TextStyle(fontWeight: FontWeight.bold), textAlign: TextAlign.center)),
              Expanded(flex: 2, child: Text('Price', style: TextStyle(fontWeight: FontWeight.bold), textAlign: TextAlign.right)),
              Expanded(flex: 2, child: Text('Total', style: TextStyle(fontWeight: FontWeight.bold), textAlign: TextAlign.right)),
            ],
          ),
        ),
        // Table rows
        ...items.map((item) => Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Colors.grey.shade200),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item['name'] ?? '',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    if (item['description'] != null && item['description'].isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        item['description'],
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  '${item['quantity'] ?? 0}',
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  '${(item['unitPrice'] ?? 0.0).toStringAsFixed(2)}ج',
                  textAlign: TextAlign.right,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  '${(item['total'] ?? 0.0).toStringAsFixed(2)}ج',
                  textAlign: TextAlign.right,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),
        )).toList(),
      ],
    );
  }

  Widget _buildTotals() {
    final subtotal = invoiceData['subtotal'] ?? 0.0;
    final discount = invoiceData['discount'] ?? 0.0;
    final tax = invoiceData['tax'] ?? 0.0;
    final shipping = invoiceData['shipping'] ?? 0.0;
    final total = invoiceData['total'] ?? 0.0;

    return Column(
      children: [
        const Divider(),
        _buildTotalRow('Subtotal:', subtotal),
        if (discount > 0) _buildTotalRow('Discount:', -discount),
        if (tax > 0) _buildTotalRow('Tax:', tax),
        if (shipping > 0) _buildTotalRow('Shipping:', shipping),
        const Divider(thickness: 2),
        _buildTotalRow(
          'Total:',
          total,
          isTotal: true,
        ),
      ],
    );
  }

  Widget _buildTotalRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 18 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)}ج',
            style: TextStyle(
              fontSize: isTotal ? 18 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (invoiceData['termsConditions'] != null && invoiceData['termsConditions'].isNotEmpty) ...[
          const Text(
            'Terms & Conditions:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            invoiceData['termsConditions'],
            style: const TextStyle(fontSize: 14),
          ),
          const SizedBox(height: 16),
        ],
        if (invoiceData['signature'] != null && invoiceData['signature'] != 'Add Signature') ...[
          const Text(
            'Signature:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            invoiceData['signature'],
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ],
    );
  }

  Widget _buildBottomActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {
                // Edit invoice
                Navigator.pop(context);
              },
              icon: const Icon(Icons.edit),
              label: const Text('Edit'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                // Save and send invoice
                Navigator.pop(context, true);
              },
              icon: const Icon(Icons.send),
              label: const Text('Send'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4285F4),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
