import 'package:flutter/material.dart';

class PaymentMethodScreen extends StatefulWidget {
  final String currentPaymentMethod;

  const PaymentMethodScreen({
    super.key,
    required this.currentPaymentMethod,
  });

  @override
  State<PaymentMethodScreen> createState() => _PaymentMethodScreenState();
}

class _PaymentMethodScreenState extends State<PaymentMethodScreen> {
  late String selectedPaymentMethod;

  final List<Map<String, dynamic>> paymentMethods = [
    {
      'name': 'Bank Transfer',
      'description': 'Direct bank transfer or wire transfer',
      'icon': Icons.account_balance,
      'color': const Color(0xFF4285F4),
      'details': 'Provide your bank account details for direct transfer',
    },
    {
      'name': 'Credit Card',
      'description': 'Visa, Mastercard, American Express',
      'icon': Icons.credit_card,
      'color': const Color(0xFF34A853),
      'details': 'Accept payments via credit or debit cards',
    },
    {
      'name': 'PayPal',
      'description': 'Online payment via PayPal',
      'icon': Icons.payment,
      'color': const Color(0xFF0070BA),
      'details': 'Secure online payments through PayPal',
    },
    {
      'name': 'Cash',
      'description': 'Cash payment on delivery or pickup',
      'icon': Icons.money,
      'color': const Color(0xFF34A853),
      'details': 'Payment in cash upon delivery or service completion',
    },
    {
      'name': 'Check',
      'description': 'Payment by check or cheque',
      'icon': Icons.receipt_long,
      'color': const Color(0xFF9AA0A6),
      'details': 'Payment by personal or business check',
    },
    {
      'name': 'Digital Wallet',
      'description': 'Apple Pay, Google Pay, Samsung Pay',
      'icon': Icons.wallet,
      'color': const Color(0xFFEA4335),
      'details': 'Mobile wallet and contactless payments',
    },
    {
      'name': 'Cryptocurrency',
      'description': 'Bitcoin, Ethereum, and other cryptocurrencies',
      'icon': Icons.currency_bitcoin,
      'color': const Color(0xFFF7931A),
      'details': 'Digital currency payments',
    },
    {
      'name': 'Online Banking',
      'description': 'Direct online banking transfer',
      'icon': Icons.computer,
      'color': const Color(0xFF4285F4),
      'details': 'Transfer through online banking platforms',
    },
  ];

  @override
  void initState() {
    super.initState();
    selectedPaymentMethod = widget.currentPaymentMethod;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4285F4),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Payment Method',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context, selectedPaymentMethod);
            },
            child: const Text(
              'Select',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Column(
              children: [
                Icon(
                  Icons.payment,
                  size: 48,
                  color: Color(0xFF4285F4),
                ),
                SizedBox(height: 16),
                Text(
                  'Choose Payment Method',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Select how you want to receive payments for this invoice',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ListView.separated(
                padding: const EdgeInsets.all(8),
                itemCount: paymentMethods.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final method = paymentMethods[index];
                  final isSelected = selectedPaymentMethod == method['name'];
                  
                  return Container(
                    margin: const EdgeInsets.symmetric(vertical: 4),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: isSelected 
                          ? Border.all(color: const Color(0xFF4285F4), width: 2)
                          : null,
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.all(16),
                      leading: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: method['color'].withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(25),
                        ),
                        child: Icon(
                          method['icon'],
                          color: method['color'],
                          size: 24,
                        ),
                      ),
                      title: Text(
                        method['name'],
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isSelected ? const Color(0xFF4285F4) : Colors.black,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 4),
                          Text(
                            method['description'],
                            style: TextStyle(
                              fontSize: 14,
                              color: isSelected 
                                  ? const Color(0xFF4285F4).withValues(alpha: 0.7)
                                  : Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            method['details'],
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade500,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                      trailing: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (isSelected)
                            const Icon(
                              Icons.check_circle,
                              color: Color(0xFF4285F4),
                              size: 24,
                            )
                          else
                            const Icon(
                              Icons.radio_button_unchecked,
                              color: Colors.grey,
                              size: 24,
                            ),
                          const SizedBox(height: 8),
                          if (method['name'] == 'Bank Transfer' || method['name'] == 'PayPal')
                            TextButton(
                              onPressed: () {
                                _showPaymentDetails(method);
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                minimumSize: const Size(0, 0),
                              ),
                              child: const Text(
                                'Setup',
                                style: TextStyle(fontSize: 12),
                              ),
                            ),
                        ],
                      ),
                      onTap: () {
                        setState(() {
                          selectedPaymentMethod = method['name'];
                        });
                      },
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  void _showPaymentDetails(Map<String, dynamic> method) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(
                    method['icon'],
                    color: method['color'],
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '${method['name']} Setup',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: method['color'].withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: method['color'].withValues(alpha: 0.3)),
                ),
                child: Column(
                  children: [
                    Icon(
                      method['icon'],
                      size: 48,
                      color: method['color'],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      method['name'],
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: method['color'],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      method['details'],
                      style: TextStyle(
                        fontSize: 14,
                        color: method['color'].withValues(alpha: 0.7),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                method['name'] == 'Bank Transfer' 
                    ? 'Add your bank account details to receive payments directly to your account.'
                    : 'Connect your PayPal account to receive secure online payments.',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        // Show setup screen (placeholder)
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('${method['name']} setup coming soon'),
                            backgroundColor: method['color'],
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: method['color'],
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Setup'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
