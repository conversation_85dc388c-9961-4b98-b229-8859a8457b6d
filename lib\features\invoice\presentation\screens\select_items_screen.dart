import 'package:flutter/material.dart';
import 'package:invoice/core/database/database_helper.dart';

class SelectItemsScreen extends StatefulWidget {
  const SelectItemsScreen({super.key});

  @override
  State<SelectItemsScreen> createState() => _SelectItemsScreenState();
}

class _SelectItemsScreenState extends State<SelectItemsScreen> {
  List<Map<String, dynamic>> availableItems = [];
  List<Map<String, dynamic>> selectedItems = [];
  bool isLoading = true;
  String searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadItems();
  }

  Future<void> _loadItems() async {
    try {
      final db = await DatabaseHelper.instance.database;
      
      // Create items table if it doesn't exist
      await db.execute('''
        CREATE TABLE IF NOT EXISTS items (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          category TEXT DEFAULT 'منتجات',
          unit_price REAL NOT NULL DEFAULT 0.0,
          unit TEXT DEFAULT 'قطعة',
          tax_rate REAL DEFAULT 0.0,
          is_active INTEGER DEFAULT 1,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      final result = await db.query(
        'items', 
        where: 'is_active = ?',
        whereArgs: [1],
        orderBy: 'created_at DESC'
      );
      
      if (mounted) {
        setState(() {
          availableItems = result;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المنتجات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<Map<String, dynamic>> get filteredItems {
    if (searchQuery.isEmpty) return availableItems;
    return availableItems.where((item) {
      final name = item['name']?.toString().toLowerCase() ?? '';
      final description = item['description']?.toString().toLowerCase() ?? '';
      final query = searchQuery.toLowerCase();
      
      return name.contains(query) || description.contains(query);
    }).toList();
  }

  void _toggleItemSelection(Map<String, dynamic> item) {
    setState(() {
      final index = selectedItems.indexWhere((selected) => selected['id'] == item['id']);
      if (index >= 0) {
        selectedItems.removeAt(index);
      } else {
        // Add quantity and amount fields
        final itemWithQuantity = Map<String, dynamic>.from(item);
        itemWithQuantity['quantity'] = 1.0;
        itemWithQuantity['amount'] = double.tryParse(item['unit_price']?.toString() ?? '0') ?? 0.0;
        selectedItems.add(itemWithQuantity);
      }
    });
  }

  void _updateQuantity(int index, double quantity) {
    setState(() {
      selectedItems[index]['quantity'] = quantity;
      final unitPrice = double.tryParse(selectedItems[index]['unit_price']?.toString() ?? '0') ?? 0.0;
      selectedItems[index]['amount'] = unitPrice * quantity;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4285F4),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'اختيار المنتجات',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (selectedItems.isNotEmpty)
            TextButton(
              onPressed: () {
                Navigator.pop(context, selectedItems);
              },
              child: Text(
                'تأكيد (${selectedItems.length})',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          if (selectedItems.isNotEmpty) _buildSelectedItemsHeader(),
          Expanded(
            child: isLoading 
                ? const Center(child: CircularProgressIndicator())
                : _buildItemsList(),
          ),
        ],
      ),
      bottomSheet: selectedItems.isNotEmpty ? _buildBottomSheet() : null,
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        onChanged: (value) {
          setState(() {
            searchQuery = value;
          });
        },
        decoration: const InputDecoration(
          hintText: 'البحث عن منتج أو خدمة...',
          prefixIcon: Icon(Icons.search, color: Color(0xFF4285F4)),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
        ),
      ),
    );
  }

  Widget _buildSelectedItemsHeader() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF4285F4),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(Icons.check_circle, color: Colors.white, size: 20),
          const SizedBox(width: 8),
          Text(
            'تم اختيار ${selectedItems.length} عنصر',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          TextButton(
            onPressed: () {
              setState(() {
                selectedItems.clear();
              });
            },
            child: const Text(
              'مسح الكل',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemsList() {
    final displayItems = filteredItems;
    
    if (displayItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              searchQuery.isEmpty ? 'لا توجد منتجات' : 'لا توجد نتائج للبحث',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              searchQuery.isEmpty 
                  ? 'يرجى إضافة منتجات من صفحة إدارة المنتجات'
                  : 'جرب كلمات بحث أخرى',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: displayItems.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final item = displayItems[index];
        final isSelected = selectedItems.any((selected) => selected['id'] == item['id']);
        return _buildItemCard(item, isSelected);
      },
    );
  }

  Widget _buildItemCard(Map<String, dynamic> item, bool isSelected) {
    final price = double.tryParse(item['unit_price']?.toString() ?? '0') ?? 0.0;
    
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: isSelected 
            ? Border.all(color: const Color(0xFF4285F4), width: 2)
            : null,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: isSelected ? const Color(0xFF4285F4) : Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            isSelected ? Icons.check : _getCategoryIcon(item['category']?.toString() ?? 'منتجات'),
            color: isSelected ? Colors.white : Colors.grey[600],
            size: 24,
          ),
        ),
        title: Text(
          item['name']?.toString() ?? 'بدون اسم',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            if (item['description'] != null && item['description'].toString().isNotEmpty)
              Text(
                item['description'].toString(),
                style: const TextStyle(color: Colors.grey),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  '${price.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    color: Color(0xFF4285F4),
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '/ ${item['unit']?.toString() ?? 'قطعة'}',
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () => _toggleItemSelection(item),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'منتجات':
        return Icons.inventory_2;
      case 'خدمات':
        return Icons.build;
      case 'استشارات':
        return Icons.psychology;
      default:
        return Icons.category;
    }
  }

  Widget _buildBottomSheet() {
    final total = selectedItems.fold<double>(
      0.0, 
      (sum, item) => sum + (double.tryParse(item['amount']?.toString() ?? '0') ?? 0.0)
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المجموع: ${total.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4285F4),
                  ),
                ),
                Text(
                  '${selectedItems.length} عنصر',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context, selectedItems);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4285F4),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'إضافة العناصر المحددة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
