import 'package:flutter/material.dart';
import 'package:invoice/core/services/settings_service.dart';

class TemplateSelectionScreen extends StatefulWidget {
  final String currentTemplate;

  const TemplateSelectionScreen({
    super.key,
    required this.currentTemplate,
  });

  @override
  State<TemplateSelectionScreen> createState() =>
      _TemplateSelectionScreenState();
}

class _TemplateSelectionScreenState extends State<TemplateSelectionScreen> {
  late String selectedTemplate;
  final SettingsService _settingsService = SettingsService();

  final List<Map<String, dynamic>> templates = [
    {
      'name': 'Modern',
      'description': 'Clean and professional design with blue accents',
      'color': const Color(0xFF4285F4),
      'icon': Icons.description,
      'preview': 'Modern layout with header, itemized list, and totals',
    },
    {
      'name': 'Classic',
      'description': 'Traditional invoice layout with simple formatting',
      'color': const Color(0xFF34A853),
      'icon': Icons.article,
      'preview': 'Classic business invoice with standard formatting',
    },
    {
      'name': 'Minimal',
      'description': 'Simple and clean design with minimal elements',
      'color': const Color(0xFF9AA0A6),
      'icon': Icons.note,
      'preview': 'Minimalist design focusing on content clarity',
    },
    {
      'name': 'Creative',
      'description': 'Colorful and creative design for creative businesses',
      'color': const Color(0xFFEA4335),
      'icon': Icons.palette,
      'preview': 'Creative layout with vibrant colors and modern styling',
    },
    {
      'name': 'Corporate',
      'description': 'Professional corporate design with formal layout',
      'color': const Color(0xFF5F6368),
      'icon': Icons.business_center,
      'preview': 'Corporate style with formal structure and branding',
    },
    {
      'name': 'Service',
      'description': 'Optimized for service-based businesses',
      'color': const Color(0xFFFBBC04),
      'icon': Icons.handyman,
      'preview': 'Service-focused layout with time and rate tracking',
    },
  ];

  @override
  void initState() {
    super.initState();
    selectedTemplate = widget.currentTemplate;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4285F4),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Invoice Templates',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              // حفظ القالب المختار في الإعدادات
              await _settingsService.saveInvoiceTemplate(selectedTemplate);
              navigator.pop(selectedTemplate);
            },
            child: const Text(
              'Select',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Column(
              children: [
                Icon(
                  Icons.description_outlined,
                  size: 48,
                  color: Color(0xFF4285F4),
                ),
                SizedBox(height: 16),
                Text(
                  'Choose Invoice Template',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Select a template that matches your business style',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ListView.separated(
                padding: const EdgeInsets.all(8),
                itemCount: templates.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final template = templates[index];
                  final isSelected = selectedTemplate == template['name'];

                  return Container(
                    margin: const EdgeInsets.symmetric(vertical: 4),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: isSelected
                          ? Border.all(color: const Color(0xFF4285F4), width: 2)
                          : null,
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.all(16),
                      leading: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: template['color'].withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(25),
                        ),
                        child: Icon(
                          template['icon'],
                          color: template['color'],
                          size: 24,
                        ),
                      ),
                      title: Text(
                        template['name'],
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isSelected
                              ? const Color(0xFF4285F4)
                              : Colors.black,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 4),
                          Text(
                            template['description'],
                            style: TextStyle(
                              fontSize: 14,
                              color: isSelected
                                  ? const Color(0xFF4285F4)
                                      .withValues(alpha: 0.7)
                                  : Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            template['preview'],
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade500,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                      trailing: SizedBox(
                        width: 80,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (isSelected)
                              const Icon(
                                Icons.check_circle,
                                color: Color(0xFF4285F4),
                                size: 20,
                              )
                            else
                              const Icon(
                                Icons.radio_button_unchecked,
                                color: Colors.grey,
                                size: 20,
                              ),
                            const SizedBox(height: 4),
                            TextButton(
                              onPressed: () {
                                _showTemplatePreview(template);
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                minimumSize: const Size(0, 0),
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: const Text(
                                'Preview',
                                style: TextStyle(fontSize: 10),
                              ),
                            ),
                          ],
                        ),
                      ),
                      onTap: () {
                        setState(() {
                          selectedTemplate = template['name'];
                        });
                      },
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  void _showTemplatePreview(Map<String, dynamic> template) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(
                    template['icon'],
                    color: template['color'],
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '${template['name']} Template',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  color: template['color'].withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                      color: template['color'].withValues(alpha: 0.3)),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        template['icon'],
                        size: 48,
                        color: template['color'],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        template['name'],
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: template['color'],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          template['preview'],
                          style: TextStyle(
                            fontSize: 12,
                            color: template['color'].withValues(alpha: 0.7),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                template['description'],
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Close'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        setState(() {
                          selectedTemplate = template['name'];
                        });
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: template['color'],
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Select'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
