import 'package:flutter/material.dart';

class TermsConditionsScreen extends StatefulWidget {
  final String currentTerms;

  const TermsConditionsScreen({
    super.key,
    required this.currentTerms,
  });

  @override
  State<TermsConditionsScreen> createState() => _TermsConditionsScreenState();
}

class _TermsConditionsScreenState extends State<TermsConditionsScreen> {
  final _termsController = TextEditingController();
  int _selectedTemplate = -1;

  final List<Map<String, String>> termsTemplates = [
    {
      'title': 'Standard Payment Terms',
      'content': 'Payment is due within 30 days of invoice date. Late payments may incur additional charges. All prices are in the specified currency and exclude applicable taxes unless otherwise stated.',
    },
    {
      'title': 'Service Terms',
      'content': 'Services will be provided as described in this invoice. Any changes to the scope of work must be agreed upon in writing. Client is responsible for providing necessary access and information.',
    },
    {
      'title': 'Product Terms',
      'content': 'All products are subject to availability. Returns are accepted within 14 days of delivery in original condition. Shipping costs are non-refundable unless the return is due to our error.',
    },
    {
      'title': 'Consulting Terms',
      'content': 'Consulting services are billed hourly as specified. Travel time and expenses may be charged separately. Confidentiality of client information will be maintained at all times.',
    },
    {
      'title': 'Digital Services',
      'content': 'Digital deliverables will be provided via email or secure download. Client is responsible for backing up received files. Revisions beyond the agreed scope may incur additional charges.',
    },
  ];

  @override
  void initState() {
    super.initState();
    _termsController.text = widget.currentTerms;
  }

  @override
  void dispose() {
    _termsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4285F4),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Terms & Conditions',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _saveTerms,
            child: const Text(
              'Save',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildTermsCard(),
            const SizedBox(height: 24),
            _buildTemplatesSection(),
            const SizedBox(height: 24),
            _buildCustomTermsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildTermsCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(40),
            ),
            child: const Icon(
              Icons.description,
              size: 40,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Terms & Conditions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Add terms and conditions to protect your business and set clear expectations',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTemplatesSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.library_books,
                color: Color(0xFF4285F4),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Quick Templates',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ),
              if (_selectedTemplate >= 0)
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedTemplate = -1;
                    });
                  },
                  child: const Text('Clear'),
                ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Choose a template to get started quickly',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          ...termsTemplates.asMap().entries.map((entry) {
            final index = entry.key;
            final template = entry.value;
            final isSelected = _selectedTemplate == index;
            
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: isSelected 
                    ? Border.all(color: const Color(0xFF4285F4), width: 2)
                    : Border.all(color: Colors.grey.shade300),
              ),
              child: ListTile(
                contentPadding: const EdgeInsets.all(12),
                title: Text(
                  template['title']!,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isSelected ? const Color(0xFF4285F4) : Colors.black,
                  ),
                ),
                subtitle: Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    template['content']!,
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected 
                          ? const Color(0xFF4285F4).withValues(alpha: 0.7)
                          : Colors.grey.shade600,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                trailing: Icon(
                  isSelected ? Icons.check_circle : Icons.radio_button_unchecked,
                  color: isSelected ? const Color(0xFF4285F4) : Colors.grey,
                ),
                onTap: () {
                  setState(() {
                    if (_selectedTemplate == index) {
                      _selectedTemplate = -1;
                      _termsController.clear();
                    } else {
                      _selectedTemplate = index;
                      _termsController.text = template['content']!;
                    }
                  });
                },
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildCustomTermsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.edit_note,
                color: Color(0xFF4285F4),
              ),
              SizedBox(width: 12),
              Text(
                'Custom Terms',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Write your own terms and conditions',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _termsController,
            maxLines: 8,
            decoration: InputDecoration(
              hintText: 'Enter your terms and conditions here...\n\nExample:\n• Payment terms\n• Delivery conditions\n• Return policy\n• Liability limitations',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFF4285F4)),
              ),
              filled: true,
              fillColor: Colors.grey.shade50,
            ),
            onChanged: (value) {
              if (value.isNotEmpty) {
                setState(() {
                  _selectedTemplate = -1;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    _termsController.clear();
                    setState(() {
                      _selectedTemplate = -1;
                    });
                  },
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _saveTerms,
                  icon: const Icon(Icons.save),
                  label: const Text('Save'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4285F4),
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _saveTerms() {
    final terms = _termsController.text.trim();
    
    Navigator.pop(context, terms);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          terms.isEmpty 
              ? 'Terms and conditions cleared'
              : 'Terms and conditions saved successfully'
        ),
        backgroundColor: Colors.green,
      ),
    );
  }
}
