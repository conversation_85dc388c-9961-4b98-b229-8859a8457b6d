import 'package:flutter/material.dart';
import 'package:invoice/features/invoice/domain/entities/invoice.dart';
import 'package:intl/intl.dart';
import 'package:invoice/core/utils/responsive_utils.dart';

class InvoiceListItem extends StatelessWidget {
  final Invoice invoice;
  final VoidCallback onTap;

  const InvoiceListItem({
    super.key,
    required this.invoice,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final daysUntilDue = invoice.dueDate.difference(DateTime.now()).inDays;
    final arabicDateFormat = DateFormat('yyyy/MM/dd');

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: ResponsiveUtils.getResponsivePadding(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Client name and invoice number
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    flex: 2,
                    child: Text(
                      '${invoice.clientName} - ${invoice.invoiceNumber}',
                      style: TextStyle(
                        fontSize:
                            ResponsiveUtils.getResponsiveFontSize(context, 16),
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(
                      width: ResponsiveUtils.getResponsiveSpacing(context, 8)),
                  Text(
                    arabicDateFormat.format(invoice.dueDate),
                    style: TextStyle(
                      fontSize:
                          ResponsiveUtils.getResponsiveFontSize(context, 14),
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              SizedBox(
                  height: ResponsiveUtils.getResponsiveSpacing(context, 12)),

              // Amount
              FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.centerLeft,
                child: Text(
                  '${NumberFormat('#,##0.00').format(invoice.total)} ${invoice.currency}',
                  style: TextStyle(
                    fontSize:
                        ResponsiveUtils.getResponsiveFontSize(context, 20),
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ),
              SizedBox(
                  height: ResponsiveUtils.getResponsiveSpacing(context, 12)),

              // Status and due date
              Wrap(
                spacing: ResponsiveUtils.getResponsiveSpacing(context, 12),
                runSpacing: ResponsiveUtils.getResponsiveSpacing(context, 8),
                children: [
                  _buildStatusChip(context),
                  Text(
                    daysUntilDue > 0
                        ? 'Due in $daysUntilDue days'
                        : daysUntilDue == 0
                            ? 'Due today'
                            : 'Overdue by ${-daysUntilDue} days',
                    style: TextStyle(
                      fontSize:
                          ResponsiveUtils.getResponsiveFontSize(context, 12),
                      color:
                          daysUntilDue < 0 ? Colors.red : Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    Color backgroundColor;
    Color textColor;
    String statusText;

    switch (invoice.status.toLowerCase()) {
      case 'paid':
        backgroundColor = Colors.green.shade100;
        textColor = Colors.green.shade700;
        statusText = 'Paid';
        break;
      case 'unpaid':
        backgroundColor = Colors.blue.shade100;
        textColor = Colors.blue.shade700;
        statusText = 'Unpaid';
        break;
      case 'overdue':
        backgroundColor = Colors.red.shade100;
        textColor = Colors.red.shade700;
        statusText = 'Overdue';
        break;
      case 'partially paid':
        backgroundColor = Colors.orange.shade100;
        textColor = Colors.orange.shade700;
        statusText = 'Partially Paid';
        break;
      default:
        backgroundColor = Colors.grey.shade100;
        textColor = Colors.grey.shade700;
        statusText = 'Draft';
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveUtils.getResponsiveSpacing(context, 8),
        vertical: ResponsiveUtils.getResponsiveSpacing(context, 4),
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: textColor,
          fontSize: ResponsiveUtils.getResponsiveFontSize(context, 12),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
