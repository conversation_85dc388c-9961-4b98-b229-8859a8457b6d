import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class InvoiceTemplates {
  static Widget buildInvoice(
      String templateName, Map<String, dynamic> invoiceData) {
    switch (templateName) {
      case 'Modern':
        return ModernTemplate(invoiceData: invoiceData);
      case 'Classic':
        return ClassicTemplate(invoiceData: invoiceData);
      case 'Minimal':
        return MinimalTemplate(invoiceData: invoiceData);
      case 'Creative':
        return CreativeTemplate(invoiceData: invoiceData);
      case 'Corporate':
        return CorporateTemplate(invoiceData: invoiceData);
      case 'Service':
        return ServiceTemplate(invoiceData: invoiceData);
      default:
        return ModernTemplate(invoiceData: invoiceData);
    }
  }
}

class ModernTemplate extends StatelessWidget {
  final Map<String, dynamic> invoiceData;

  const ModernTemplate({super.key, required this.invoiceData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildModernHeader(),
        const SizedBox(height: 32),
        _buildBusinessInfo(),
        const SizedBox(height: 24),
        _buildClientInfo(),
        const SizedBox(height: 32),
        _buildItemsTable(),
        const SizedBox(height: 24),
        _buildTotals(),
        const SizedBox(height: 32),
        _buildFooter(),
      ],
    );
  }

  Widget _buildModernHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Logo placeholder
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Center(
                  child: Text(
                    'LOGO',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              // Invoice title with orange accent
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF9800),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'INVOICE',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Invoice #',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    invoiceData['invoiceNumber'] ?? 'INV00001',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const Text(
                    'Date',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    DateFormat('dd/MM/yyyy')
                        .format(invoiceData['createdDate'] ?? DateTime.now()),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const Text(
                    'Due Date',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    DateFormat('dd/MM/yyyy')
                        .format(invoiceData['dueDate'] ?? DateTime.now()),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessInfo() {
    final businessInfo = invoiceData['businessInfo'] as Map<String, dynamic>?;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'From:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4285F4),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            businessInfo?['name'] ?? 'Your Business Name',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (businessInfo?['email'] != null) ...[
            const SizedBox(height: 4),
            Text(businessInfo!['email'], style: const TextStyle(fontSize: 14)),
          ],
          if (businessInfo?['phone'] != null) ...[
            const SizedBox(height: 4),
            Text(businessInfo!['phone'], style: const TextStyle(fontSize: 14)),
          ],
          if (businessInfo?['address'] != null) ...[
            const SizedBox(height: 4),
            Text(businessInfo!['address'],
                style: const TextStyle(fontSize: 14)),
          ],
        ],
      ),
    );
  }

  Widget _buildClientInfo() {
    final clientInfo = invoiceData['clientInfo'] as Map<String, dynamic>?;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'To:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            clientInfo?['name'] ?? 'Client Name',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (clientInfo?['company'] != null &&
              clientInfo!['company'].isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(clientInfo['company'], style: const TextStyle(fontSize: 14)),
          ],
          if (clientInfo?['email'] != null) ...[
            const SizedBox(height: 4),
            Text(clientInfo!['email'], style: const TextStyle(fontSize: 14)),
          ],
          if (clientInfo?['phone'] != null) ...[
            const SizedBox(height: 4),
            Text(clientInfo!['phone'], style: const TextStyle(fontSize: 14)),
          ],
          if (clientInfo?['address'] != null) ...[
            const SizedBox(height: 4),
            Text(clientInfo!['address'], style: const TextStyle(fontSize: 14)),
          ],
        ],
      ),
    );
  }

  Widget _buildItemsTable() {
    final items = invoiceData['items'] as List<dynamic>? ?? [];

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Expanded(
                    flex: 3,
                    child: Text('Description',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.white))),
                Expanded(
                    flex: 1,
                    child: Text('Qty',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.white),
                        textAlign: TextAlign.center)),
                Expanded(
                    flex: 2,
                    child: Text('Rate',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.white),
                        textAlign: TextAlign.right)),
                Expanded(
                    flex: 2,
                    child: Text('Amount',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.white),
                        textAlign: TextAlign.right)),
              ],
            ),
          ),
          // Table rows
          ...items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: index % 2 == 0 ? Colors.white : Colors.grey.shade50,
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['name'] ?? '',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        if (item['description'] != null &&
                            item['description'].isNotEmpty) ...[
                          const SizedBox(height: 4),
                          Text(
                            item['description'],
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      '${item['quantity'] ?? 0}',
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '${(item['unitPrice'] ?? 0.0).toStringAsFixed(2)}ج',
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '${(item['total'] ?? 0.0).toStringAsFixed(2)}ج',
                      textAlign: TextAlign.right,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildTotals() {
    final subtotal = invoiceData['subtotal'] ?? 0.0;
    final discount = invoiceData['discount'] ?? 0.0;
    final tax = invoiceData['tax'] ?? 0.0;
    final shipping = invoiceData['shipping'] ?? 0.0;
    final total = invoiceData['total'] ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          _buildTotalRow('Subtotal:', subtotal),
          if (discount > 0) _buildTotalRow('Discount:', -discount),
          if (tax > 0) _buildTotalRow('Tax:', tax),
          if (shipping > 0) _buildTotalRow('Shipping:', shipping),
          const Divider(thickness: 2),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF4285F4),
              borderRadius: BorderRadius.circular(4),
            ),
            child: _buildTotalRow(
              'Total:',
              total,
              isTotal: true,
              textColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalRow(String label, double amount,
      {bool isTotal = false, Color? textColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 18 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: textColor,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)}ج',
            style: TextStyle(
              fontSize: isTotal ? 18 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (invoiceData['termsConditions'] != null &&
            invoiceData['termsConditions'].isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Terms & Conditions:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4285F4),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  invoiceData['termsConditions'],
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
        if (invoiceData['signature'] != null &&
            invoiceData['signature'] != 'Add Signature') ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Signature:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  invoiceData['signature'],
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}

class ClassicTemplate extends StatelessWidget {
  final Map<String, dynamic> invoiceData;
  const ClassicTemplate({super.key, required this.invoiceData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildClassicHeader(),
        const SizedBox(height: 32),
        _buildBusinessClientRow(),
        const SizedBox(height: 32),
        _buildItemsTable(),
        const SizedBox(height: 24),
        _buildTotals(),
        const SizedBox(height: 32),
        _buildFooter(),
      ],
    );
  }

  Widget _buildClassicHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'INVOICE',
              style: TextStyle(
                fontSize: 36,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'Invoice #: ${invoiceData['invoiceNumber'] ?? 'INV00001'}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Date: ${DateFormat('MMM dd, yyyy').format(invoiceData['createdDate'] ?? DateTime.now())}',
                  style: const TextStyle(fontSize: 14),
                ),
                Text(
                  'Due: ${DateFormat('MMM dd, yyyy').format(invoiceData['dueDate'] ?? DateTime.now())}',
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          height: 2,
          color: Colors.black,
        ),
      ],
    );
  }

  Widget _buildBusinessClientRow() {
    final businessInfo = invoiceData['businessInfo'] as Map<String, dynamic>?;
    final clientInfo = invoiceData['clientInfo'] as Map<String, dynamic>?;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'FROM:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                businessInfo?['name'] ?? 'Your Business Name',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (businessInfo?['address'] != null) ...[
                const SizedBox(height: 4),
                Text(businessInfo!['address'],
                    style: const TextStyle(fontSize: 14)),
              ],
              if (businessInfo?['phone'] != null) ...[
                const SizedBox(height: 4),
                Text(businessInfo!['phone'],
                    style: const TextStyle(fontSize: 14)),
              ],
              if (businessInfo?['email'] != null) ...[
                const SizedBox(height: 4),
                Text(businessInfo!['email'],
                    style: const TextStyle(fontSize: 14)),
              ],
            ],
          ),
        ),
        const SizedBox(width: 32),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'TO:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                clientInfo?['name'] ?? 'Client Name',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (clientInfo?['company'] != null &&
                  clientInfo!['company'].isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(clientInfo['company'],
                    style: const TextStyle(fontSize: 14)),
              ],
              if (clientInfo?['address'] != null) ...[
                const SizedBox(height: 4),
                Text(clientInfo!['address'],
                    style: const TextStyle(fontSize: 14)),
              ],
              if (clientInfo?['phone'] != null) ...[
                const SizedBox(height: 4),
                Text(clientInfo!['phone'],
                    style: const TextStyle(fontSize: 14)),
              ],
              if (clientInfo?['email'] != null) ...[
                const SizedBox(height: 4),
                Text(clientInfo!['email'],
                    style: const TextStyle(fontSize: 14)),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildItemsTable() {
    final items = invoiceData['items'] as List<dynamic>? ?? [];

    return Column(
      children: [
        // Table header
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: const BoxDecoration(
            border: Border(
              top: BorderSide(color: Colors.black, width: 2),
              bottom: BorderSide(color: Colors.black, width: 1),
            ),
          ),
          child: const Row(
            children: [
              Expanded(
                  flex: 3,
                  child: Text('DESCRIPTION',
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          letterSpacing: 0.5))),
              Expanded(
                  flex: 1,
                  child: Text('QTY',
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          letterSpacing: 0.5),
                      textAlign: TextAlign.center)),
              Expanded(
                  flex: 2,
                  child: Text('RATE',
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          letterSpacing: 0.5),
                      textAlign: TextAlign.right)),
              Expanded(
                  flex: 2,
                  child: Text('AMOUNT',
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          letterSpacing: 0.5),
                      textAlign: TextAlign.right)),
            ],
          ),
        ),
        // Table rows
        ...items
            .map((item) => Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey, width: 0.5),
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 3,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item['name'] ?? '',
                              style: const TextStyle(
                                  fontWeight: FontWeight.w500, fontSize: 14),
                            ),
                            if (item['description'] != null &&
                                item['description'].isNotEmpty) ...[
                              const SizedBox(height: 2),
                              Text(
                                item['description'],
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          '${item['quantity'] ?? 0}',
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          '${(item['unitPrice'] ?? 0.0).toStringAsFixed(2)}',
                          textAlign: TextAlign.right,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          '${(item['total'] ?? 0.0).toStringAsFixed(2)}',
                          textAlign: TextAlign.right,
                          style: const TextStyle(
                              fontWeight: FontWeight.w500, fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
        // Bottom border
        Container(
          height: 2,
          color: Colors.black,
        ),
      ],
    );
  }

  Widget _buildTotals() {
    final subtotal = invoiceData['subtotal'] ?? 0.0;
    final discount = invoiceData['discount'] ?? 0.0;
    final tax = invoiceData['tax'] ?? 0.0;
    final shipping = invoiceData['shipping'] ?? 0.0;
    final total = invoiceData['total'] ?? 0.0;

    return Column(
      children: [
        const SizedBox(height: 16),
        Row(
          children: [
            const Expanded(flex: 3, child: SizedBox()),
            Expanded(
              flex: 2,
              child: Column(
                children: [
                  _buildTotalRow('Subtotal:', subtotal),
                  if (discount > 0) _buildTotalRow('Discount:', -discount),
                  if (tax > 0) _buildTotalRow('Tax:', tax),
                  if (shipping > 0) _buildTotalRow('Shipping:', shipping),
                  const Divider(thickness: 2, color: Colors.black),
                  _buildTotalRow('TOTAL:', total, isTotal: true),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTotalRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              letterSpacing: isTotal ? 0.5 : 0,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ${invoiceData['currency'] ?? ''}',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (invoiceData['termsConditions'] != null &&
            invoiceData['termsConditions'].isNotEmpty) ...[
          const Text(
            'TERMS & CONDITIONS:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            invoiceData['termsConditions'],
            style: const TextStyle(fontSize: 12),
          ),
          const SizedBox(height: 16),
        ],
        if (invoiceData['signature'] != null &&
            invoiceData['signature'] != 'Add Signature' &&
            invoiceData['signature'].isNotEmpty) ...[
          const Text(
            'AUTHORIZED SIGNATURE:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            invoiceData['signature'],
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ],
    );
  }
}

class MinimalTemplate extends StatelessWidget {
  final Map<String, dynamic> invoiceData;
  const MinimalTemplate({super.key, required this.invoiceData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMinimalHeader(),
        const SizedBox(height: 40),
        _buildContactInfo(),
        const SizedBox(height: 40),
        _buildSimpleItemsList(),
        const SizedBox(height: 32),
        _buildMinimalTotals(),
        const SizedBox(height: 40),
        _buildMinimalFooter(),
      ],
    );
  }

  Widget _buildMinimalHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Invoice',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.w300,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          invoiceData['invoiceNumber'] ?? 'INV00001',
          style: const TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          DateFormat('MMMM dd, yyyy')
              .format(invoiceData['createdDate'] ?? DateTime.now()),
          style: const TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildContactInfo() {
    final businessInfo = invoiceData['businessInfo'] as Map<String, dynamic>?;
    final clientInfo = invoiceData['clientInfo'] as Map<String, dynamic>?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Business info
        Text(
          businessInfo?['name'] ?? 'Your Business',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        if (businessInfo?['email'] != null) ...[
          const SizedBox(height: 2),
          Text(
            businessInfo!['email'],
            style: const TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],

        const SizedBox(height: 24),

        // Client info
        const Text(
          'Bill to:',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          clientInfo?['name'] ?? 'Client Name',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        if (clientInfo?['email'] != null) ...[
          const SizedBox(height: 2),
          Text(
            clientInfo!['email'],
            style: const TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ],
    );
  }

  Widget _buildSimpleItemsList() {
    final items = invoiceData['items'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...items
            .map((item) => Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 3,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item['name'] ?? '',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            if (item['description'] != null &&
                                item['description'].isNotEmpty) ...[
                              const SizedBox(height: 2),
                              Text(
                                item['description'],
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        '${item['quantity'] ?? 0} × ${(item['unitPrice'] ?? 0.0).toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        '${(item['total'] ?? 0.0).toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
      ],
    );
  }

  Widget _buildMinimalTotals() {
    final subtotal = invoiceData['subtotal'] ?? 0.0;
    final discount = invoiceData['discount'] ?? 0.0;
    final tax = invoiceData['tax'] ?? 0.0;
    final shipping = invoiceData['shipping'] ?? 0.0;
    final total = invoiceData['total'] ?? 0.0;

    return Column(
      children: [
        Container(
          height: 1,
          color: Colors.grey.shade300,
          margin: const EdgeInsets.symmetric(vertical: 16),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Subtotal',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            Text(
              '${subtotal.toStringAsFixed(2)} ${invoiceData['currency'] ?? ''}',
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
        if (discount > 0) ...[
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Discount',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
              Text(
                '-${discount.toStringAsFixed(2)} ${invoiceData['currency'] ?? ''}',
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        ],
        if (tax > 0) ...[
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Tax',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
              Text(
                '${tax.toStringAsFixed(2)} ${invoiceData['currency'] ?? ''}',
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        ],
        if (shipping > 0) ...[
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Shipping',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
              Text(
                '${shipping.toStringAsFixed(2)} ${invoiceData['currency'] ?? ''}',
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        ],
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Total',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${total.toStringAsFixed(2)} ${invoiceData['currency'] ?? ''}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMinimalFooter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (invoiceData['termsConditions'] != null &&
            invoiceData['termsConditions'].isNotEmpty) ...[
          const Text(
            'Notes',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            invoiceData['termsConditions'],
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ],
    );
  }
}

class CreativeTemplate extends StatelessWidget {
  final Map<String, dynamic> invoiceData;
  const CreativeTemplate({super.key, required this.invoiceData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCreativeHeader(),
        const SizedBox(height: 32),
        _buildCreativeContactInfo(),
        const SizedBox(height: 32),
        _buildCreativeItemsTable(),
        const SizedBox(height: 24),
        _buildCreativeTotals(),
        const SizedBox(height: 32),
        _buildCreativeFooter(),
      ],
    );
  }

  Widget _buildCreativeHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF00C853), Color(0xFF4CAF50)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Text(
                    'LOGO',
                    style: TextStyle(
                      color: Color(0xFF00C853),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'INVOICE',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  invoiceData['invoiceNumber'] ?? 'INV00001',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF00C853),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'Date: ${DateFormat('dd/MM/yyyy').format(invoiceData['createdDate'] ?? DateTime.now())}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
              Text(
                'Due: ${DateFormat('dd/MM/yyyy').format(invoiceData['dueDate'] ?? DateTime.now())}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCreativeContactInfo() {
    final businessInfo = invoiceData['businessInfo'] as Map<String, dynamic>?;
    final clientInfo = invoiceData['clientInfo'] as Map<String, dynamic>?;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF00C853), width: 2),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'From',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF00C853),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  businessInfo?['name'] ?? 'Your Business',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (businessInfo?['email'] != null) ...[
                  const SizedBox(height: 4),
                  Text(businessInfo!['email']),
                ],
                if (businessInfo?['phone'] != null) ...[
                  const SizedBox(height: 4),
                  Text(businessInfo!['phone']),
                ],
                if (businessInfo?['address'] != null) ...[
                  const SizedBox(height: 4),
                  Text(businessInfo!['address']),
                ],
              ],
            ),
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Bill To',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF00C853),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  clientInfo?['name'] ?? 'Client Name',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (clientInfo?['company'] != null) ...[
                  const SizedBox(height: 4),
                  Text(clientInfo!['company']),
                ],
                if (clientInfo?['email'] != null) ...[
                  const SizedBox(height: 4),
                  Text(clientInfo!['email']),
                ],
                if (clientInfo?['phone'] != null) ...[
                  const SizedBox(height: 4),
                  Text(clientInfo!['phone']),
                ],
                if (clientInfo?['address'] != null) ...[
                  const SizedBox(height: 4),
                  Text(clientInfo!['address']),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCreativeItemsTable() {
    final items = invoiceData['items'] as List<dynamic>? ?? [];

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF00C853), width: 2),
      ),
      child: Column(
        children: [
          // Table header with green gradient
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFF00C853), Color(0xFF4CAF50)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: const Row(
              children: [
                Expanded(
                    flex: 3,
                    child: Text('Description',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 16))),
                Expanded(
                    flex: 1,
                    child: Text('Qty',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 16),
                        textAlign: TextAlign.center)),
                Expanded(
                    flex: 2,
                    child: Text('Rate',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 16),
                        textAlign: TextAlign.right)),
                Expanded(
                    flex: 2,
                    child: Text('Amount',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 16),
                        textAlign: TextAlign.right)),
              ],
            ),
          ),
          // Table rows
          ...items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: index % 2 == 0 ? Colors.white : const Color(0xFFF1F8E9),
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['name'] ?? '',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                          ),
                        ),
                        if (item['description'] != null &&
                            item['description'].isNotEmpty) ...[
                          const SizedBox(height: 4),
                          Text(
                            item['description'],
                            style: const TextStyle(
                              fontSize: 13,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      '${item['quantity'] ?? 0}',
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 15),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '\$${(item['unitPrice'] ?? 0.0).toStringAsFixed(2)}',
                      textAlign: TextAlign.right,
                      style: const TextStyle(fontSize: 15),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '\$${(item['total'] ?? 0.0).toStringAsFixed(2)}',
                      textAlign: TextAlign.right,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 15,
                        color: Color(0xFF00C853),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildCreativeTotals() {
    final subtotal = invoiceData['subtotal'] ?? 0.0;
    final discount = invoiceData['discount'] ?? 0.0;
    final tax = invoiceData['tax'] ?? 0.0;
    final shipping = invoiceData['shipping'] ?? 0.0;
    final total = invoiceData['total'] ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF00C853), Color(0xFF4CAF50)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          if (subtotal > 0) _buildCreativeTotalRow('Subtotal', subtotal, false),
          if (discount > 0)
            _buildCreativeTotalRow('Discount', -discount, false),
          if (tax > 0) _buildCreativeTotalRow('Tax', tax, false),
          if (shipping > 0) _buildCreativeTotalRow('Shipping', shipping, false),
          const Divider(color: Colors.white54, thickness: 2),
          _buildCreativeTotalRow('Total', total, true),
        ],
      ),
    );
  }

  Widget _buildCreativeTotalRow(String label, double amount, bool isTotal) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: Colors.white,
            ),
          ),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreativeFooter() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF00C853)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (invoiceData['termsConditions'] != null &&
              invoiceData['termsConditions'].isNotEmpty) ...[
            const Text(
              'Terms & Conditions',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF00C853),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              invoiceData['termsConditions'],
              style: const TextStyle(fontSize: 14),
            ),
          ],
          if (invoiceData['signature'] != null &&
              invoiceData['signature'].isNotEmpty) ...[
            const SizedBox(height: 20),
            const Text(
              'Signature',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF00C853),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              invoiceData['signature'],
              style: const TextStyle(
                fontSize: 18,
                fontStyle: FontStyle.italic,
                color: Color(0xFF00C853),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class CorporateTemplate extends StatelessWidget {
  final Map<String, dynamic> invoiceData;
  const CorporateTemplate({super.key, required this.invoiceData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCorporateHeader(),
        const SizedBox(height: 32),
        _buildCorporateContactInfo(),
        const SizedBox(height: 32),
        _buildCorporateItemsTable(),
        const SizedBox(height: 24),
        _buildCorporateTotals(),
        const SizedBox(height: 32),
        _buildCorporateFooter(),
      ],
    );
  }

  Widget _buildCorporateHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF1976D2), Color(0xFF2196F3)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Center(
                  child: Text(
                    'LOGO',
                    style: TextStyle(
                      color: Color(0xFF1976D2),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'INVOICE',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'Invoice #',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
                Text(
                  invoiceData['invoiceNumber'] ?? 'INV00001',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1976D2),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Date: ${DateFormat('dd/MM/yyyy').format(invoiceData['createdDate'] ?? DateTime.now())}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                Text(
                  'Due: ${DateFormat('dd/MM/yyyy').format(invoiceData['dueDate'] ?? DateTime.now())}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCorporateContactInfo() {
    final businessInfo = invoiceData['businessInfo'] as Map<String, dynamic>?;
    final clientInfo = invoiceData['clientInfo'] as Map<String, dynamic>?;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFFF3F7FF),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFF1976D2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1976D2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'FROM',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  businessInfo?['name'] ?? 'Your Business',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (businessInfo?['email'] != null) ...[
                  const SizedBox(height: 4),
                  Text(businessInfo!['email']),
                ],
                if (businessInfo?['phone'] != null) ...[
                  const SizedBox(height: 4),
                  Text(businessInfo!['phone']),
                ],
                if (businessInfo?['address'] != null) ...[
                  const SizedBox(height: 4),
                  Text(businessInfo!['address']),
                ],
              ],
            ),
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade700,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'BILL TO',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  clientInfo?['name'] ?? 'Client Name',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (clientInfo?['company'] != null) ...[
                  const SizedBox(height: 4),
                  Text(clientInfo!['company']),
                ],
                if (clientInfo?['email'] != null) ...[
                  const SizedBox(height: 4),
                  Text(clientInfo!['email']),
                ],
                if (clientInfo?['phone'] != null) ...[
                  const SizedBox(height: 4),
                  Text(clientInfo!['phone']),
                ],
                if (clientInfo?['address'] != null) ...[
                  const SizedBox(height: 4),
                  Text(clientInfo!['address']),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCorporateItemsTable() {
    final items = invoiceData['items'] as List<dynamic>? ?? [];

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFF1976D2), width: 2),
      ),
      child: Column(
        children: [
          // Table header with blue gradient
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFF1976D2), Color(0xFF2196F3)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Expanded(
                    flex: 3,
                    child: Text('Description',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 16))),
                Expanded(
                    flex: 1,
                    child: Text('Qty',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 16),
                        textAlign: TextAlign.center)),
                Expanded(
                    flex: 2,
                    child: Text('Rate',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 16),
                        textAlign: TextAlign.right)),
                Expanded(
                    flex: 2,
                    child: Text('Amount',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 16),
                        textAlign: TextAlign.right)),
              ],
            ),
          ),
          // Table rows
          ...items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: index % 2 == 0 ? Colors.white : const Color(0xFFF3F7FF),
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['name'] ?? '',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                          ),
                        ),
                        if (item['description'] != null &&
                            item['description'].isNotEmpty) ...[
                          const SizedBox(height: 4),
                          Text(
                            item['description'],
                            style: const TextStyle(
                              fontSize: 13,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      '${item['quantity'] ?? 0}',
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 15),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '\$${(item['unitPrice'] ?? 0.0).toStringAsFixed(2)}',
                      textAlign: TextAlign.right,
                      style: const TextStyle(fontSize: 15),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '\$${(item['total'] ?? 0.0).toStringAsFixed(2)}',
                      textAlign: TextAlign.right,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 15,
                        color: Color(0xFF1976D2),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildCorporateTotals() {
    final subtotal = invoiceData['subtotal'] ?? 0.0;
    final discount = invoiceData['discount'] ?? 0.0;
    final tax = invoiceData['tax'] ?? 0.0;
    final shipping = invoiceData['shipping'] ?? 0.0;
    final total = invoiceData['total'] ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF3F7FF),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFF1976D2)),
      ),
      child: Column(
        children: [
          if (subtotal > 0)
            _buildCorporateTotalRow('Subtotal', subtotal, false),
          if (discount > 0)
            _buildCorporateTotalRow('Discount', -discount, false),
          if (tax > 0) _buildCorporateTotalRow('Tax', tax, false),
          if (shipping > 0)
            _buildCorporateTotalRow('Shipping', shipping, false),
          Container(
            height: 2,
            color: const Color(0xFF1976D2),
            margin: const EdgeInsets.symmetric(vertical: 12),
          ),
          _buildCorporateTotalRow('Total', total, true),
        ],
      ),
    );
  }

  Widget _buildCorporateTotalRow(String label, double amount, bool isTotal) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: isTotal ? const Color(0xFF1976D2) : Colors.black87,
            ),
          ),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: isTotal ? const Color(0xFF1976D2) : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCorporateFooter() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (invoiceData['termsConditions'] != null &&
              invoiceData['termsConditions'].isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF1976D2),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'Terms & Conditions',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              invoiceData['termsConditions'],
              style: const TextStyle(fontSize: 14),
            ),
          ],
          if (invoiceData['signature'] != null &&
              invoiceData['signature'].isNotEmpty) ...[
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.grey.shade700,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'Signature',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              invoiceData['signature'],
              style: const TextStyle(
                fontSize: 18,
                fontStyle: FontStyle.italic,
                color: Color(0xFF1976D2),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class ServiceTemplate extends StatelessWidget {
  final Map<String, dynamic> invoiceData;
  const ServiceTemplate({super.key, required this.invoiceData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildServiceHeader(),
        const SizedBox(height: 32),
        _buildServiceContactInfo(),
        const SizedBox(height: 32),
        _buildServiceItemsTable(),
        const SizedBox(height: 24),
        _buildServiceTotals(),
        const SizedBox(height: 32),
        _buildServiceFooter(),
      ],
    );
  }

  Widget _buildServiceHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.shade800,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Center(
                  child: Text(
                    'LOGO',
                    style: TextStyle(
                      color: Colors.grey.shade800,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'SERVICE INVOICE',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'Invoice #',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
                Text(
                  invoiceData['invoiceNumber'] ?? 'INV00001',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Date: ${DateFormat('dd/MM/yyyy').format(invoiceData['createdDate'] ?? DateTime.now())}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                Text(
                  'Due: ${DateFormat('dd/MM/yyyy').format(invoiceData['dueDate'] ?? DateTime.now())}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceContactInfo() {
    final businessInfo = invoiceData['businessInfo'] as Map<String, dynamic>?;
    final clientInfo = invoiceData['clientInfo'] as Map<String, dynamic>?;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade400),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Service Provider',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  businessInfo?['name'] ?? 'Your Business',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (businessInfo?['email'] != null) ...[
                  const SizedBox(height: 4),
                  Text(businessInfo!['email']),
                ],
                if (businessInfo?['phone'] != null) ...[
                  const SizedBox(height: 4),
                  Text(businessInfo!['phone']),
                ],
                if (businessInfo?['address'] != null) ...[
                  const SizedBox(height: 4),
                  Text(businessInfo!['address']),
                ],
              ],
            ),
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Client',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  clientInfo?['name'] ?? 'Client Name',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (clientInfo?['company'] != null) ...[
                  const SizedBox(height: 4),
                  Text(clientInfo!['company']),
                ],
                if (clientInfo?['email'] != null) ...[
                  const SizedBox(height: 4),
                  Text(clientInfo!['email']),
                ],
                if (clientInfo?['phone'] != null) ...[
                  const SizedBox(height: 4),
                  Text(clientInfo!['phone']),
                ],
                if (clientInfo?['address'] != null) ...[
                  const SizedBox(height: 4),
                  Text(clientInfo!['address']),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildServiceItemsTable() {
    final items = invoiceData['items'] as List<dynamic>? ?? [];

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade400, width: 1),
      ),
      child: Column(
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade800,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Expanded(
                    flex: 3,
                    child: Text('Service Description',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 16))),
                Expanded(
                    flex: 1,
                    child: Text('Hours',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 16),
                        textAlign: TextAlign.center)),
                Expanded(
                    flex: 2,
                    child: Text('Rate',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 16),
                        textAlign: TextAlign.right)),
                Expanded(
                    flex: 2,
                    child: Text('Amount',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 16),
                        textAlign: TextAlign.right)),
              ],
            ),
          ),
          // Table rows
          ...items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: index % 2 == 0 ? Colors.white : Colors.grey.shade50,
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['name'] ?? '',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                          ),
                        ),
                        if (item['description'] != null &&
                            item['description'].isNotEmpty) ...[
                          const SizedBox(height: 4),
                          Text(
                            item['description'],
                            style: const TextStyle(
                              fontSize: 13,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      '${item['quantity'] ?? 0}',
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 15),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '\$${(item['unitPrice'] ?? 0.0).toStringAsFixed(2)}',
                      textAlign: TextAlign.right,
                      style: const TextStyle(fontSize: 15),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '\$${(item['total'] ?? 0.0).toStringAsFixed(2)}',
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 15,
                        color: Colors.grey.shade800,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildServiceTotals() {
    final subtotal = invoiceData['subtotal'] ?? 0.0;
    final discount = invoiceData['discount'] ?? 0.0;
    final tax = invoiceData['tax'] ?? 0.0;
    final shipping = invoiceData['shipping'] ?? 0.0;
    final total = invoiceData['total'] ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade400),
      ),
      child: Column(
        children: [
          if (subtotal > 0) _buildServiceTotalRow('Subtotal', subtotal, false),
          if (discount > 0) _buildServiceTotalRow('Discount', -discount, false),
          if (tax > 0) _buildServiceTotalRow('Tax', tax, false),
          if (shipping > 0)
            _buildServiceTotalRow('Additional Fees', shipping, false),
          Container(
            height: 2,
            color: Colors.grey.shade800,
            margin: const EdgeInsets.symmetric(vertical: 12),
          ),
          _buildServiceTotalRow('Total Amount', total, true),
        ],
      ),
    );
  }

  Widget _buildServiceTotalRow(String label, double amount, bool isTotal) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: isTotal ? Colors.grey.shade800 : Colors.black87,
            ),
          ),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: isTotal ? Colors.grey.shade800 : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceFooter() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (invoiceData['termsConditions'] != null &&
              invoiceData['termsConditions'].isNotEmpty) ...[
            Text(
              'Terms & Conditions',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              invoiceData['termsConditions'],
              style: const TextStyle(fontSize: 14),
            ),
          ],
          if (invoiceData['signature'] != null &&
              invoiceData['signature'].isNotEmpty) ...[
            const SizedBox(height: 20),
            Text(
              'Authorized Signature',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              invoiceData['signature'],
              style: TextStyle(
                fontSize: 18,
                fontStyle: FontStyle.italic,
                color: Colors.grey.shade800,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
