import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class InvoiceTemplates {
  static Widget buildInvoice(
      String templateName, Map<String, dynamic> invoiceData) {
    switch (templateName) {
      case 'Modern':
        return ModernTemplate(invoiceData: invoiceData);
      case 'Classic':
        return ClassicTemplate(invoiceData: invoiceData);
      case 'Minimal':
        return MinimalTemplate(invoiceData: invoiceData);
      case 'Creative':
        return CreativeTemplate(invoiceData: invoiceData);
      case 'Corporate':
        return CorporateTemplate(invoiceData: invoiceData);
      case 'Service':
        return ServiceTemplate(invoiceData: invoiceData);
      default:
        return ModernTemplate(invoiceData: invoiceData);
    }
  }
}

class ModernTemplate extends StatelessWidget {
  final Map<String, dynamic> invoiceData;

  const ModernTemplate({super.key, required this.invoiceData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildModernHeader(),
        const SizedBox(height: 32),
        _buildBusinessInfo(),
        const SizedBox(height: 24),
        _buildClientInfo(),
        const SizedBox(height: 32),
        _buildItemsTable(),
        const SizedBox(height: 24),
        _buildTotals(),
        const SizedBox(height: 32),
        _buildFooter(),
      ],
    );
  }

  Widget _buildModernHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF4285F4), Color(0xFF1976D2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            'INVOICE',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                invoiceData['invoiceNumber'] ?? 'INV00001',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Date: ${DateFormat('dd/MM/yyyy').format(invoiceData['createdDate'] ?? DateTime.now())}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                ),
              ),
              Text(
                'Due: ${DateFormat('dd/MM/yyyy').format(invoiceData['dueDate'] ?? DateTime.now())}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessInfo() {
    final businessInfo = invoiceData['businessInfo'] as Map<String, dynamic>?;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'From:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4285F4),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            businessInfo?['name'] ?? 'Your Business Name',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (businessInfo?['email'] != null) ...[
            const SizedBox(height: 4),
            Text(businessInfo!['email'], style: const TextStyle(fontSize: 14)),
          ],
          if (businessInfo?['phone'] != null) ...[
            const SizedBox(height: 4),
            Text(businessInfo!['phone'], style: const TextStyle(fontSize: 14)),
          ],
          if (businessInfo?['address'] != null) ...[
            const SizedBox(height: 4),
            Text(businessInfo!['address'],
                style: const TextStyle(fontSize: 14)),
          ],
        ],
      ),
    );
  }

  Widget _buildClientInfo() {
    final clientInfo = invoiceData['clientInfo'] as Map<String, dynamic>?;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'To:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            clientInfo?['name'] ?? 'Client Name',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (clientInfo?['company'] != null &&
              clientInfo!['company'].isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(clientInfo['company'], style: const TextStyle(fontSize: 14)),
          ],
          if (clientInfo?['email'] != null) ...[
            const SizedBox(height: 4),
            Text(clientInfo!['email'], style: const TextStyle(fontSize: 14)),
          ],
          if (clientInfo?['phone'] != null) ...[
            const SizedBox(height: 4),
            Text(clientInfo!['phone'], style: const TextStyle(fontSize: 14)),
          ],
          if (clientInfo?['address'] != null) ...[
            const SizedBox(height: 4),
            Text(clientInfo!['address'], style: const TextStyle(fontSize: 14)),
          ],
        ],
      ),
    );
  }

  Widget _buildItemsTable() {
    final items = invoiceData['items'] as List<dynamic>? ?? [];

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF4285F4),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Expanded(
                    flex: 3,
                    child: Text('Item',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.white))),
                Expanded(
                    flex: 1,
                    child: Text('Qty',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.white),
                        textAlign: TextAlign.center)),
                Expanded(
                    flex: 2,
                    child: Text('Price',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.white),
                        textAlign: TextAlign.right)),
                Expanded(
                    flex: 2,
                    child: Text('Total',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.white),
                        textAlign: TextAlign.right)),
              ],
            ),
          ),
          // Table rows
          ...items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: index % 2 == 0 ? Colors.white : Colors.grey.shade50,
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['name'] ?? '',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        if (item['description'] != null &&
                            item['description'].isNotEmpty) ...[
                          const SizedBox(height: 4),
                          Text(
                            item['description'],
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      '${item['quantity'] ?? 0}',
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '${(item['unitPrice'] ?? 0.0).toStringAsFixed(2)}ج',
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '${(item['total'] ?? 0.0).toStringAsFixed(2)}ج',
                      textAlign: TextAlign.right,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildTotals() {
    final subtotal = invoiceData['subtotal'] ?? 0.0;
    final discount = invoiceData['discount'] ?? 0.0;
    final tax = invoiceData['tax'] ?? 0.0;
    final shipping = invoiceData['shipping'] ?? 0.0;
    final total = invoiceData['total'] ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          _buildTotalRow('Subtotal:', subtotal),
          if (discount > 0) _buildTotalRow('Discount:', -discount),
          if (tax > 0) _buildTotalRow('Tax:', tax),
          if (shipping > 0) _buildTotalRow('Shipping:', shipping),
          const Divider(thickness: 2),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF4285F4),
              borderRadius: BorderRadius.circular(4),
            ),
            child: _buildTotalRow(
              'Total:',
              total,
              isTotal: true,
              textColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalRow(String label, double amount,
      {bool isTotal = false, Color? textColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 18 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: textColor,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)}ج',
            style: TextStyle(
              fontSize: isTotal ? 18 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (invoiceData['termsConditions'] != null &&
            invoiceData['termsConditions'].isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Terms & Conditions:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4285F4),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  invoiceData['termsConditions'],
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
        if (invoiceData['signature'] != null &&
            invoiceData['signature'] != 'Add Signature') ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Signature:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  invoiceData['signature'],
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}

class ClassicTemplate extends StatelessWidget {
  final Map<String, dynamic> invoiceData;
  const ClassicTemplate({super.key, required this.invoiceData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildClassicHeader(),
        const SizedBox(height: 32),
        _buildBusinessClientRow(),
        const SizedBox(height: 32),
        _buildItemsTable(),
        const SizedBox(height: 24),
        _buildTotals(),
        const SizedBox(height: 32),
        _buildFooter(),
      ],
    );
  }

  Widget _buildClassicHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'INVOICE',
              style: TextStyle(
                fontSize: 36,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'Invoice #: ${invoiceData['invoiceNumber'] ?? 'INV00001'}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Date: ${DateFormat('MMM dd, yyyy').format(invoiceData['createdDate'] ?? DateTime.now())}',
                  style: const TextStyle(fontSize: 14),
                ),
                Text(
                  'Due: ${DateFormat('MMM dd, yyyy').format(invoiceData['dueDate'] ?? DateTime.now())}',
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          height: 2,
          color: Colors.black,
        ),
      ],
    );
  }

  Widget _buildBusinessClientRow() {
    final businessInfo = invoiceData['businessInfo'] as Map<String, dynamic>?;
    final clientInfo = invoiceData['clientInfo'] as Map<String, dynamic>?;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'FROM:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                businessInfo?['name'] ?? 'Your Business Name',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (businessInfo?['address'] != null) ...[
                const SizedBox(height: 4),
                Text(businessInfo!['address'],
                    style: const TextStyle(fontSize: 14)),
              ],
              if (businessInfo?['phone'] != null) ...[
                const SizedBox(height: 4),
                Text(businessInfo!['phone'],
                    style: const TextStyle(fontSize: 14)),
              ],
              if (businessInfo?['email'] != null) ...[
                const SizedBox(height: 4),
                Text(businessInfo!['email'],
                    style: const TextStyle(fontSize: 14)),
              ],
            ],
          ),
        ),
        const SizedBox(width: 32),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'TO:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                clientInfo?['name'] ?? 'Client Name',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (clientInfo?['company'] != null &&
                  clientInfo!['company'].isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(clientInfo['company'],
                    style: const TextStyle(fontSize: 14)),
              ],
              if (clientInfo?['address'] != null) ...[
                const SizedBox(height: 4),
                Text(clientInfo!['address'],
                    style: const TextStyle(fontSize: 14)),
              ],
              if (clientInfo?['phone'] != null) ...[
                const SizedBox(height: 4),
                Text(clientInfo!['phone'],
                    style: const TextStyle(fontSize: 14)),
              ],
              if (clientInfo?['email'] != null) ...[
                const SizedBox(height: 4),
                Text(clientInfo!['email'],
                    style: const TextStyle(fontSize: 14)),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildItemsTable() {
    final items = invoiceData['items'] as List<dynamic>? ?? [];

    return Column(
      children: [
        // Table header
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: const BoxDecoration(
            border: Border(
              top: BorderSide(color: Colors.black, width: 2),
              bottom: BorderSide(color: Colors.black, width: 1),
            ),
          ),
          child: const Row(
            children: [
              Expanded(
                  flex: 3,
                  child: Text('DESCRIPTION',
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          letterSpacing: 0.5))),
              Expanded(
                  flex: 1,
                  child: Text('QTY',
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          letterSpacing: 0.5),
                      textAlign: TextAlign.center)),
              Expanded(
                  flex: 2,
                  child: Text('RATE',
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          letterSpacing: 0.5),
                      textAlign: TextAlign.right)),
              Expanded(
                  flex: 2,
                  child: Text('AMOUNT',
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          letterSpacing: 0.5),
                      textAlign: TextAlign.right)),
            ],
          ),
        ),
        // Table rows
        ...items
            .map((item) => Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey, width: 0.5),
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 3,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item['name'] ?? '',
                              style: const TextStyle(
                                  fontWeight: FontWeight.w500, fontSize: 14),
                            ),
                            if (item['description'] != null &&
                                item['description'].isNotEmpty) ...[
                              const SizedBox(height: 2),
                              Text(
                                item['description'],
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          '${item['quantity'] ?? 0}',
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          '${(item['unitPrice'] ?? 0.0).toStringAsFixed(2)}',
                          textAlign: TextAlign.right,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          '${(item['total'] ?? 0.0).toStringAsFixed(2)}',
                          textAlign: TextAlign.right,
                          style: const TextStyle(
                              fontWeight: FontWeight.w500, fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
        // Bottom border
        Container(
          height: 2,
          color: Colors.black,
        ),
      ],
    );
  }

  Widget _buildTotals() {
    final subtotal = invoiceData['subtotal'] ?? 0.0;
    final discount = invoiceData['discount'] ?? 0.0;
    final tax = invoiceData['tax'] ?? 0.0;
    final shipping = invoiceData['shipping'] ?? 0.0;
    final total = invoiceData['total'] ?? 0.0;

    return Column(
      children: [
        const SizedBox(height: 16),
        Row(
          children: [
            const Expanded(flex: 3, child: SizedBox()),
            Expanded(
              flex: 2,
              child: Column(
                children: [
                  _buildTotalRow('Subtotal:', subtotal),
                  if (discount > 0) _buildTotalRow('Discount:', -discount),
                  if (tax > 0) _buildTotalRow('Tax:', tax),
                  if (shipping > 0) _buildTotalRow('Shipping:', shipping),
                  const Divider(thickness: 2, color: Colors.black),
                  _buildTotalRow('TOTAL:', total, isTotal: true),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTotalRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              letterSpacing: isTotal ? 0.5 : 0,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ${invoiceData['currency'] ?? ''}',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (invoiceData['termsConditions'] != null &&
            invoiceData['termsConditions'].isNotEmpty) ...[
          const Text(
            'TERMS & CONDITIONS:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            invoiceData['termsConditions'],
            style: const TextStyle(fontSize: 12),
          ),
          const SizedBox(height: 16),
        ],
        if (invoiceData['signature'] != null &&
            invoiceData['signature'] != 'Add Signature' &&
            invoiceData['signature'].isNotEmpty) ...[
          const Text(
            'AUTHORIZED SIGNATURE:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            invoiceData['signature'],
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ],
    );
  }
}

class MinimalTemplate extends StatelessWidget {
  final Map<String, dynamic> invoiceData;
  const MinimalTemplate({super.key, required this.invoiceData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMinimalHeader(),
        const SizedBox(height: 40),
        _buildContactInfo(),
        const SizedBox(height: 40),
        _buildSimpleItemsList(),
        const SizedBox(height: 32),
        _buildMinimalTotals(),
        const SizedBox(height: 40),
        _buildMinimalFooter(),
      ],
    );
  }

  Widget _buildMinimalHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Invoice',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.w300,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          invoiceData['invoiceNumber'] ?? 'INV00001',
          style: const TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          DateFormat('MMMM dd, yyyy')
              .format(invoiceData['createdDate'] ?? DateTime.now()),
          style: const TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildContactInfo() {
    final businessInfo = invoiceData['businessInfo'] as Map<String, dynamic>?;
    final clientInfo = invoiceData['clientInfo'] as Map<String, dynamic>?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Business info
        Text(
          businessInfo?['name'] ?? 'Your Business',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        if (businessInfo?['email'] != null) ...[
          const SizedBox(height: 2),
          Text(
            businessInfo!['email'],
            style: const TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],

        const SizedBox(height: 24),

        // Client info
        const Text(
          'Bill to:',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          clientInfo?['name'] ?? 'Client Name',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        if (clientInfo?['email'] != null) ...[
          const SizedBox(height: 2),
          Text(
            clientInfo!['email'],
            style: const TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ],
    );
  }

  Widget _buildSimpleItemsList() {
    final items = invoiceData['items'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...items
            .map((item) => Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 3,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item['name'] ?? '',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            if (item['description'] != null &&
                                item['description'].isNotEmpty) ...[
                              const SizedBox(height: 2),
                              Text(
                                item['description'],
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        '${item['quantity'] ?? 0} × ${(item['unitPrice'] ?? 0.0).toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        '${(item['total'] ?? 0.0).toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
      ],
    );
  }

  Widget _buildMinimalTotals() {
    final subtotal = invoiceData['subtotal'] ?? 0.0;
    final discount = invoiceData['discount'] ?? 0.0;
    final tax = invoiceData['tax'] ?? 0.0;
    final shipping = invoiceData['shipping'] ?? 0.0;
    final total = invoiceData['total'] ?? 0.0;

    return Column(
      children: [
        Container(
          height: 1,
          color: Colors.grey.shade300,
          margin: const EdgeInsets.symmetric(vertical: 16),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Subtotal',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            Text(
              '${subtotal.toStringAsFixed(2)} ${invoiceData['currency'] ?? ''}',
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
        if (discount > 0) ...[
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Discount',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
              Text(
                '-${discount.toStringAsFixed(2)} ${invoiceData['currency'] ?? ''}',
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        ],
        if (tax > 0) ...[
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Tax',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
              Text(
                '${tax.toStringAsFixed(2)} ${invoiceData['currency'] ?? ''}',
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        ],
        if (shipping > 0) ...[
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Shipping',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
              Text(
                '${shipping.toStringAsFixed(2)} ${invoiceData['currency'] ?? ''}',
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        ],
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Total',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${total.toStringAsFixed(2)} ${invoiceData['currency'] ?? ''}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMinimalFooter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (invoiceData['termsConditions'] != null &&
            invoiceData['termsConditions'].isNotEmpty) ...[
          const Text(
            'Notes',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            invoiceData['termsConditions'],
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ],
    );
  }
}

class CreativeTemplate extends StatelessWidget {
  final Map<String, dynamic> invoiceData;
  const CreativeTemplate({super.key, required this.invoiceData});

  @override
  Widget build(BuildContext context) {
    return ModernTemplate(invoiceData: invoiceData); // Temporary fallback
  }
}

class CorporateTemplate extends StatelessWidget {
  final Map<String, dynamic> invoiceData;
  const CorporateTemplate({super.key, required this.invoiceData});

  @override
  Widget build(BuildContext context) {
    return ModernTemplate(invoiceData: invoiceData); // Temporary fallback
  }
}

class ServiceTemplate extends StatelessWidget {
  final Map<String, dynamic> invoiceData;
  const ServiceTemplate({super.key, required this.invoiceData});

  @override
  Widget build(BuildContext context) {
    return ModernTemplate(invoiceData: invoiceData); // Temporary fallback
  }
}
