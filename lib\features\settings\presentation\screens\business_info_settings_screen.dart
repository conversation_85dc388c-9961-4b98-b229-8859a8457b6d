import 'package:flutter/material.dart';
import 'package:invoice/core/database/database_helper.dart';
import 'package:sqflite/sqflite.dart';

class BusinessInfoSettingsScreen extends StatefulWidget {
  const BusinessInfoSettingsScreen({super.key});

  @override
  State<BusinessInfoSettingsScreen> createState() =>
      _BusinessInfoSettingsScreenState();
}

class _BusinessInfoSettingsScreenState
    extends State<BusinessInfoSettingsScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers for business info
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _websiteController = TextEditingController();
  final _taxIdController = TextEditingController();
  final _companyController = TextEditingController();

  // Controllers for business settings
  final _taxRateController = TextEditingController();
  final _paymentMethodController = TextEditingController();
  final _termsController = TextEditingController();
  final _signatureController = TextEditingController();

  String _taxType = 'percentage';
  bool isLoading = true;
  bool isSaving = false;

  final List<String> paymentMethods = [
    'Bank Transfer',
    'Cash',
    'Credit Card',
    'PayPal',
    'Check',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _websiteController.dispose();
    _taxIdController.dispose();
    _companyController.dispose();
    _taxRateController.dispose();
    _paymentMethodController.dispose();
    _termsController.dispose();
    _signatureController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final dbHelper = DatabaseHelper.instance;

      // Load business info
      final db = await dbHelper.database;
      final businessResult = await db.query('business_info', limit: 1);

      if (businessResult.isNotEmpty) {
        final business = businessResult.first;
        _nameController.text = business['name'] as String? ?? '';
        _emailController.text = business['email'] as String? ?? '';
        _phoneController.text = business['phone'] as String? ?? '';
        _addressController.text = business['address'] as String? ?? '';
        _websiteController.text = business['website'] as String? ?? '';
        _taxIdController.text = business['tax_id'] as String? ?? '';
        _companyController.text = business['company'] as String? ?? '';
      }

      // Load business settings
      final businessSettings = await dbHelper.getSettingsByCategory('business');
      _taxRateController.text = businessSettings['tax_rate'] ?? '15';
      _taxType = businessSettings['tax_type'] ?? 'percentage';
      _paymentMethodController.text =
          businessSettings['payment_method'] ?? 'Bank Transfer';
      _termsController.text = businessSettings['terms_conditions'] ??
          'Payment is due within 30 days';
      _signatureController.text = businessSettings['signature'] ?? '';

      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading business data: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> _saveData() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      isSaving = true;
    });

    try {
      final dbHelper = DatabaseHelper.instance;
      final db = await dbHelper.database;
      final now = DateTime.now().toIso8601String();

      // Save business info
      await db.insert(
        'business_info',
        {
          'id': 1,
          'name': _nameController.text,
          'email': _emailController.text,
          'phone': _phoneController.text,
          'address': _addressController.text,
          'website': _websiteController.text,
          'tax_id': _taxIdController.text,
          'company': _companyController.text,
          'created_at': now,
          'updated_at': now,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      // Save business settings
      await dbHelper.setSetting(
          'tax_rate', _taxRateController.text, 'business');
      await dbHelper.setSetting('tax_type', _taxType, 'business');
      await dbHelper.setSetting(
          'payment_method', _paymentMethodController.text, 'business');
      await dbHelper.setSetting(
          'terms_conditions', _termsController.text, 'business');
      await dbHelper.setSetting(
          'signature', _signatureController.text, 'business');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Business information saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving business information: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: const Text(
          'Business Settings',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2196F3),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          if (!isLoading)
            TextButton(
              onPressed: isSaving ? null : _saveData,
              child: Text(
                isSaving ? 'Saving...' : 'Save',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Business Information Section
                    _buildSectionCard(
                      title: 'Business Information',
                      icon: Icons.business,
                      children: [
                        _buildTextField(
                          controller: _nameController,
                          label: 'Business Name',
                          icon: Icons.business,
                          required: true,
                        ),
                        const SizedBox(height: 16),
                        _buildTextField(
                          controller: _emailController,
                          label: 'Email',
                          icon: Icons.email,
                          keyboardType: TextInputType.emailAddress,
                          required: true,
                        ),
                        const SizedBox(height: 16),
                        _buildTextField(
                          controller: _phoneController,
                          label: 'Phone',
                          icon: Icons.phone,
                          keyboardType: TextInputType.phone,
                          required: true,
                        ),
                        const SizedBox(height: 16),
                        _buildTextField(
                          controller: _addressController,
                          label: 'Address',
                          icon: Icons.location_on,
                          maxLines: 3,
                          required: true,
                        ),
                        const SizedBox(height: 16),
                        _buildTextField(
                          controller: _websiteController,
                          label: 'Website',
                          icon: Icons.web,
                          keyboardType: TextInputType.url,
                        ),
                        const SizedBox(height: 16),
                        _buildTextField(
                          controller: _taxIdController,
                          label: 'Tax ID',
                          icon: Icons.receipt_long,
                        ),
                        const SizedBox(height: 16),
                        _buildTextField(
                          controller: _companyController,
                          label: 'Company Registration',
                          icon: Icons.business_center,
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Tax Settings Section
                    _buildSectionCard(
                      title: 'Tax Settings',
                      icon: Icons.receipt,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: _buildTextField(
                                controller: _taxRateController,
                                label: 'Tax Rate',
                                icon: Icons.percent,
                                keyboardType: TextInputType.number,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              flex: 3,
                              child: DropdownButtonFormField<String>(
                                value: _taxType,
                                decoration: const InputDecoration(
                                  labelText: 'Tax Type',
                                  border: OutlineInputBorder(),
                                ),
                                items: const [
                                  DropdownMenuItem(
                                      value: 'percentage',
                                      child: Text('Percentage')),
                                  DropdownMenuItem(
                                      value: 'fixed',
                                      child: Text('Fixed Amount')),
                                ],
                                onChanged: (value) {
                                  setState(() {
                                    _taxType = value!;
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Payment & Terms Section
                    _buildSectionCard(
                      title: 'Payment & Terms',
                      icon: Icons.payment,
                      children: [
                        DropdownButtonFormField<String>(
                          value: _paymentMethodController.text.isNotEmpty
                              ? _paymentMethodController.text
                              : null,
                          decoration: const InputDecoration(
                            labelText: 'Default Payment Method',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.payment),
                          ),
                          items: paymentMethods.map((method) {
                            return DropdownMenuItem(
                              value: method,
                              child: Text(method),
                            );
                          }).toList(),
                          onChanged: (value) {
                            _paymentMethodController.text = value ?? '';
                          },
                        ),
                        const SizedBox(height: 16),
                        _buildTextField(
                          controller: _termsController,
                          label: 'Terms & Conditions',
                          icon: Icons.description,
                          maxLines: 4,
                        ),
                        const SizedBox(height: 16),
                        _buildTextField(
                          controller: _signatureController,
                          label: 'Signature',
                          icon: Icons.draw,
                          maxLines: 2,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: const Color(0xFF2196F3)),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    bool required = false,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label + (required ? ' *' : ''),
        border: const OutlineInputBorder(),
        prefixIcon: Icon(icon),
      ),
      validator: required
          ? (value) {
              if (value == null || value.isEmpty) {
                return 'This field is required';
              }
              return null;
            }
          : null,
    );
  }
}
