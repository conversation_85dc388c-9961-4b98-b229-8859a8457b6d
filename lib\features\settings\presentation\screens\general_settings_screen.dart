import 'package:flutter/material.dart';
import 'package:invoice/core/database/database_helper.dart';
import 'package:invoice/core/services/settings_service.dart';

class GeneralSettingsScreen extends StatefulWidget {
  const GeneralSettingsScreen({super.key});

  @override
  State<GeneralSettingsScreen> createState() => _GeneralSettingsScreenState();
}

class _GeneralSettingsScreenState extends State<GeneralSettingsScreen> {
  Map<String, String> settings = {};
  bool isLoading = true;

  final List<String> currencies = [
    'SAR',
    'USD',
    'EUR',
    'GBP',
    'AED',
    'KWD',
    'QAR',
    'BHD',
    'OMR',
    'JOD'
  ];

  final List<String> numberFormats = [
    '1,000,000.00',
    '1.000.000,00',
    '1 000 000.00',
    '1000000.00',
  ];

  final List<String> dateFormats = [
    'yyyy/MM/dd',
    'dd/MM/yyyy',
    'MM/dd/yyyy',
    'dd-MM-yyyy',
    'yyyy-MM-dd',
  ];

  final List<String> languages = [
    'Arabic',
    'English',
    'French',
    'Spanish',
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final dbHelper = DatabaseHelper.instance;
      final generalSettings = await dbHelper.getSettingsByCategory('general');

      if (mounted) {
        setState(() {
          settings = generalSettings;
          isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading general settings: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> _updateSetting(String key, String value) async {
    try {
      final dbHelper = DatabaseHelper.instance;
      await dbHelper.setSetting(key, value, 'general');

      setState(() {
        settings[key] = value;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Setting updated successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating setting: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: const Text(
          'General Settings',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2196F3),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildSettingCard(
                    title: 'Default Currency',
                    currentValue: settings['default_currency'] ?? 'SAR',
                    options: currencies,
                    onChanged: (value) =>
                        _updateSetting('default_currency', value),
                    icon: Icons.attach_money,
                  ),
                  const SizedBox(height: 16),
                  _buildSettingCard(
                    title: 'Number Format',
                    currentValue: settings['number_format'] ?? '1,000,000.00',
                    options: numberFormats,
                    onChanged: (value) =>
                        _updateSetting('number_format', value),
                    icon: Icons.format_list_numbered,
                  ),
                  const SizedBox(height: 16),
                  _buildSettingCard(
                    title: 'Date Format',
                    currentValue: settings['date_format'] ?? 'yyyy/MM/dd',
                    options: dateFormats,
                    onChanged: (value) => _updateSetting('date_format', value),
                    icon: Icons.date_range,
                  ),
                  const SizedBox(height: 16),
                  _buildSettingCard(
                    title: 'Language',
                    currentValue: settings['language'] ?? 'Arabic',
                    options: languages,
                    onChanged: (value) => _updateSetting('language', value),
                    icon: Icons.language,
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSettingCard({
    required String title,
    required String currentValue,
    required List<String> options,
    required ValueChanged<String> onChanged,
    required IconData icon,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: const Color(0xFF2196F3)),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: currentValue,
                  isExpanded: true,
                  items: options.map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(
                        value,
                        style: const TextStyle(fontSize: 16),
                      ),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      onChanged(newValue);
                    }
                  },
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Current: $currentValue',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
