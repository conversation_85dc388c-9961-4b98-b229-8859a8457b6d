import 'package:flutter/material.dart';
import 'package:invoice/core/database/database_helper.dart';

class InvoiceSettingsScreen extends StatefulWidget {
  const InvoiceSettingsScreen({super.key});

  @override
  State<InvoiceSettingsScreen> createState() => _InvoiceSettingsScreenState();
}

class _InvoiceSettingsScreenState extends State<InvoiceSettingsScreen> {
  Map<String, String> settings = {};
  bool isLoading = true;

  final List<String> dueTermsOptions = [
    '7 days',
    '14 days',
    '30 days',
    '45 days',
    '60 days',
    '90 days',
    'Net 10',
    'Net 15',
    'Net 30',
    'Due on receipt',
  ];

  final List<String> templateOptions = [
    'Modern',
    'Classic',
    'Minimal',
    'Professional',
    'Creative',
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final dbHelper = DatabaseHelper.instance;
      final invoiceSettings = await dbHelper.getSettingsByCategory('invoice');
      
      if (mounted) {
        setState(() {
          settings = invoiceSettings;
          isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading invoice settings: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> _updateSetting(String key, String value) async {
    try {
      final dbHelper = DatabaseHelper.instance;
      await dbHelper.setSetting(key, value, 'invoice');
      
      setState(() {
        settings[key] = value;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Setting updated successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating setting: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: const Text(
          'Invoice Settings',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2196F3),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildSettingCard(
                    title: 'Due Terms',
                    currentValue: settings['due_terms'] ?? '7 days',
                    options: dueTermsOptions,
                    onChanged: (value) => _updateSetting('due_terms', value),
                    icon: Icons.schedule,
                    description: 'Default payment due terms for new invoices',
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildSwitchCard(
                    title: 'Show Paid on Invoice',
                    value: settings['show_paid_on_invoice'] == 'true',
                    onChanged: (value) => _updateSetting('show_paid_on_invoice', value.toString()),
                    icon: Icons.visibility,
                    description: 'Display "PAID" stamp on paid invoices',
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildSettingCard(
                    title: 'Default Template',
                    currentValue: settings['default_template'] ?? 'Modern',
                    options: templateOptions,
                    onChanged: (value) => _updateSetting('default_template', value),
                    icon: Icons.design_services,
                    description: 'Default invoice template for new invoices',
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildInfoCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildSettingCard({
    required String title,
    required String currentValue,
    required List<String> options,
    required ValueChanged<String> onChanged,
    required IconData icon,
    required String description,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: const Color(0xFF2196F3)),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: currentValue,
                  isExpanded: true,
                  items: options.map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(
                        value,
                        style: const TextStyle(fontSize: 16),
                      ),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      onChanged(newValue);
                    }
                  },
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Current: $currentValue',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchCard({
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
    required String description,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: const Color(0xFF2196F3)),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Switch(
                  value: value,
                  onChanged: onChanged,
                  activeColor: const Color(0xFF2196F3),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue[600]),
                const SizedBox(width: 12),
                const Text(
                  'Invoice Settings Info',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoItem(
              'Due Terms',
              'Sets the default payment terms for new invoices. You can still change this for individual invoices.',
            ),
            const SizedBox(height: 12),
            _buildInfoItem(
              'Show Paid Status',
              'When enabled, paid invoices will display a "PAID" stamp when viewed or printed.',
            ),
            const SizedBox(height: 12),
            _buildInfoItem(
              'Default Template',
              'Choose the default design template for new invoices. Templates affect the visual appearance of your invoices.',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String title, String description) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          description,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}
