import 'package:flutter/material.dart';
import 'package:invoice/core/database/database_helper.dart';
import 'package:invoice/core/services/settings_service.dart';
import 'package:invoice/features/settings/presentation/screens/business_info_settings_screen.dart';
import 'package:invoice/features/settings/presentation/screens/general_settings_screen.dart';
import 'package:invoice/features/settings/presentation/screens/invoice_settings_screen.dart';
import 'package:url_launcher/url_launcher.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  Map<String, String> settings = {};
  bool isLoading = true;
  final SettingsService _settingsService = SettingsService();

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      await _settingsService.initialize();

      // Try to load settings, if table doesn't exist, reset database
      try {
        final dbHelper = DatabaseHelper.instance;
        final loadedSettings = await dbHelper.getAllSettings();
        if (mounted) {
          setState(() {
            settings = loadedSettings;
            isLoading = false;
          });
        }
      } catch (tableError) {
        debugPrint('Settings table not found, resetting database...');
        final dbHelper = DatabaseHelper.instance;
        await dbHelper.resetDatabase();

        // Try again after reset
        final loadedSettings = await dbHelper.getAllSettings();
        if (mounted) {
          setState(() {
            settings = loadedSettings;
            isLoading = false;
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading settings: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: const Text(
          'Settings',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2196F3),
        elevation: 0,
        centerTitle: true,
        automaticallyImplyLeading:
            false, // Remove back button for persistent navigation
        actions: [
          IconButton(
            icon: const Icon(Icons.menu, color: Colors.white),
            onPressed: () {},
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // General Section
                  _buildSectionCard(
                    title: 'General',
                    children: [
                      _buildSettingTile(
                        title: 'Default Currency',
                        subtitle: settings['default_currency'] ?? 'SAR',
                        icon: Icons.attach_money,
                        onTap: () => _navigateToGeneralSettings(),
                      ),
                      _buildSettingTile(
                        title: 'Number Format',
                        subtitle: settings['number_format'] ?? '1,000,000.00',
                        icon: Icons.format_list_numbered,
                        onTap: () => _navigateToGeneralSettings(),
                      ),
                      _buildSettingTile(
                        title: 'Date Format',
                        subtitle: settings['date_format'] ?? 'yyyy/MM/dd',
                        icon: Icons.date_range,
                        onTap: () => _navigateToGeneralSettings(),
                      ),
                      _buildSettingTile(
                        title: 'Language',
                        subtitle: settings['language'] ?? 'Arabic',
                        icon: Icons.language,
                        onTap: () => _navigateToGeneralSettings(),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Business Section
                  _buildSectionCard(
                    title: 'Business',
                    children: [
                      _buildSettingTile(
                        title: 'Business Info',
                        subtitle: '',
                        icon: Icons.business,
                        onTap: () => _navigateToBusinessInfo(),
                      ),
                      _buildSettingTile(
                        title: 'Switch Business',
                        subtitle: '',
                        icon: Icons.swap_horiz,
                        onTap: () => _showSwitchBusinessDialog(),
                      ),
                      _buildSettingTile(
                        title: 'Tax',
                        subtitle: '${settings['tax_rate'] ?? '15'}%',
                        icon: Icons.receipt,
                        onTap: () => _navigateToBusinessInfo(),
                      ),
                      _buildSettingTile(
                        title: 'Payment Method',
                        subtitle: settings['payment_method'] ?? 'Bank Transfer',
                        icon: Icons.payment,
                        onTap: () => _navigateToBusinessInfo(),
                      ),
                      _buildSettingTile(
                        title: 'Terms & Conditions',
                        subtitle: '',
                        icon: Icons.description,
                        onTap: () => _navigateToBusinessInfo(),
                      ),
                      _buildSettingTile(
                        title: 'Signature',
                        subtitle: '',
                        icon: Icons.draw,
                        onTap: () => _navigateToBusinessInfo(),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Invoice Section
                  _buildSectionCard(
                    title: 'Invoice',
                    children: [
                      _buildSettingTile(
                        title: 'Due Terms',
                        subtitle: settings['due_terms'] ?? '7 days',
                        icon: Icons.schedule,
                        onTap: () => _navigateToInvoiceSettings(),
                      ),
                      _buildSettingTile(
                        title: 'Update Invoice Templates',
                        subtitle: 'Apply new templates to existing invoices',
                        icon: Icons.refresh,
                        onTap: () => _updateInvoiceTemplates(),
                      ),
                      _buildSwitchTile(
                        title: 'Show Paid on Invoice',
                        value: settings['show_paid_on_invoice'] == 'true',
                        onChanged: (value) => _updateSetting(
                            'show_paid_on_invoice',
                            value.toString(),
                            'invoice'),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // About Section
                  _buildSectionCard(
                    title: 'About',
                    children: [
                      _buildSettingTile(
                        title: 'Help Us Translate',
                        subtitle: '',
                        icon: Icons.translate,
                        onTap: () => _launchURL(
                            'https://github.com/your-repo/translations'),
                      ),
                      _buildSettingTile(
                        title: 'Feedback',
                        subtitle: '',
                        icon: Icons.feedback,
                        onTap: () => _launchURL('mailto:<EMAIL>'),
                      ),
                      _buildSettingTile(
                        title: 'Privacy Policy',
                        subtitle: '',
                        icon: Icons.privacy_tip,
                        onTap: () => _launchURL('https://yourapp.com/privacy'),
                      ),
                      _buildSettingTile(
                        title: 'Rate Us',
                        subtitle: '',
                        icon: Icons.star,
                        onTap: () => _launchURL(
                            'https://play.google.com/store/apps/details?id=com.example.invoice'),
                      ),
                      _buildSettingTile(
                        title: 'Share App',
                        subtitle: '',
                        icon: Icons.share,
                        onTap: () => _shareApp(),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Version
                  Text(
                    '*********** Version',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),

                  const SizedBox(height: 100), // Space for bottom navigation
                ],
              ),
            ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title.isNotEmpty)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey,
                ),
              ),
            ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSettingTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: const Color(0xFF2196F3)),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: subtitle.isNotEmpty
          ? Text(
              subtitle,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            )
          : null,
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: onTap,
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      leading: const Icon(Icons.visibility, color: Color(0xFF2196F3)),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFF2196F3),
      ),
    );
  }

  Future<void> _updateSetting(String key, String value, String category) async {
    try {
      final dbHelper = DatabaseHelper.instance;
      await dbHelper.setSetting(key, value, category);

      setState(() {
        settings[key] = value;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Setting updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating setting: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _navigateToGeneralSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const GeneralSettingsScreen(),
      ),
    ).then((_) => _loadSettings());
  }

  void _navigateToBusinessInfo() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const BusinessInfoSettingsScreen(),
      ),
    ).then((_) => _loadSettings());
  }

  void _navigateToInvoiceSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const InvoiceSettingsScreen(),
      ),
    ).then((_) => _loadSettings());
  }

  void _showSwitchBusinessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Switch Business'),
        content:
            const Text('This feature will be available in future updates.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not launch URL'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _shareApp() {
    // Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality will be implemented'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  Future<void> _updateInvoiceTemplates() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Invoice Templates'),
        content: const Text(
            'This will update all existing invoices to use the new Modern template. '
            'This action cannot be undone. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Update'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Updating templates...'),
            ],
          ),
        ),
      );

      final db = await DatabaseHelper.instance.database;

      // Update all invoices to use Modern template
      final result = await db.rawUpdate('''
        UPDATE invoices
        SET template = ?
        WHERE template IS NULL OR template = '' OR template = 'Default'
      ''', ['Modern']);

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Successfully updated $result invoices to use Modern template'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating templates: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
