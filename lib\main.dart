import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:invoice/core/di/injection_container.dart' as di;
import 'package:invoice/features/invoice/presentation/bloc/invoice_bloc.dart';
import 'package:invoice/core/navigation/main_navigation_wrapper.dart';
import 'package:invoice/core/services/language_service.dart';
import 'package:invoice/core/localization/app_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await di.init();
  await LanguageService.instance.initialize();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: LanguageService.instance,
      builder: (context, child) {
        final languageService = LanguageService.instance;

        return MaterialApp(
          title: 'Invoice App',
          // Localization support
          localizationsDelegates: [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: AppLocalizations.supportedLocales,
          locale: Locale(languageService.languageCode),

          // Theme with Arabic font support
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
            useMaterial3: true,
            fontFamily: languageService.primaryFontFamily,
            // Ensure proper text direction
            textTheme: Theme.of(context).textTheme.apply(
                  fontFamily: languageService.primaryFontFamily,
                ),
          ),

          // Set app-wide text direction
          builder: (context, child) {
            return Directionality(
              textDirection: languageService.textDirection,
              child: child!,
            );
          },

          home: BlocProvider(
            create: (context) =>
                di.sl<InvoiceBloc>()..add(const InvoiceEvent.started()),
            child: const MainNavigationWrapper(),
          ),
        );
      },
    );
  }
}
