import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:invoice/core/di/injection_container.dart' as di;
import 'package:invoice/features/invoice/presentation/bloc/invoice_bloc.dart';
import 'package:invoice/core/navigation/main_navigation_wrapper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await di.init();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Invoice App',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: BlocProvider(
        create: (context) =>
            di.sl<InvoiceBloc>()..add(const InvoiceEvent.started()),
        child: const MainNavigationWrapper(),
      ),
    );
  }
}
