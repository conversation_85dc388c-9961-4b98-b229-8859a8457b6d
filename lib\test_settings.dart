import 'package:flutter/material.dart';
import 'package:invoice/core/services/settings_service.dart';
import 'package:invoice/core/database/database_helper.dart';

class TestSettingsScreen extends StatefulWidget {
  const TestSettingsScreen({super.key});

  @override
  State<TestSettingsScreen> createState() => _TestSettingsScreenState();
}

class _TestSettingsScreenState extends State<TestSettingsScreen> {
  final SettingsService _settingsService = SettingsService();
  String _status = 'Initializing...';
  Map<String, String> _settings = {};

  @override
  void initState() {
    super.initState();
    _testSettings();
  }

  Future<void> _testSettings() async {
    try {
      setState(() {
        _status = 'Testing database connection...';
      });

      // Test database connection
      final dbHelper = DatabaseHelper.instance;
      final db = await dbHelper.database;
      
      setState(() {
        _status = 'Database connected. Testing settings service...';
      });

      // Initialize settings service
      await _settingsService.initialize();
      
      setState(() {
        _status = 'Settings service initialized. Loading settings...';
      });

      // Test loading settings
      final currency = await _settingsService.getDefaultCurrency();
      final taxRate = await _settingsService.getTaxRate();
      final paymentMethod = await _settingsService.getPaymentMethod();
      final dueTerms = await _settingsService.getDueTerms();
      
      setState(() {
        _status = 'Settings loaded successfully!';
        _settings = {
          'Currency': currency,
          'Tax Rate': taxRate.toString(),
          'Payment Method': paymentMethod,
          'Due Terms': dueTerms,
        };
      });

    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  Future<void> _resetDatabase() async {
    try {
      setState(() {
        _status = 'Resetting database...';
      });

      final dbHelper = DatabaseHelper.instance;
      await dbHelper.resetDatabase();
      
      setState(() {
        _status = 'Database reset. Retesting...';
      });

      await _testSettings();
    } catch (e) {
      setState(() {
        _status = 'Reset error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Status:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _status,
                      style: TextStyle(
                        fontSize: 16,
                        color: _status.contains('Error') ? Colors.red : Colors.green,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (_settings.isNotEmpty) ...[
              const Text(
                'Loaded Settings:',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: _settings.entries.map((entry) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                '${entry.key}:',
                                style: const TextStyle(fontWeight: FontWeight.w500),
                              ),
                            ),
                            Expanded(
                              flex: 3,
                              child: Text(entry.value),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton(
                  onPressed: _testSettings,
                  child: const Text('Retry Test'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _resetDatabase,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Reset Database'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
