@echo off
chcp 65001 >nul
cd /d "d:\invoice"

echo Setting English locale...
set LANG=en_US.UTF-8
set LC_ALL=en_US.UTF-8
set GRADLE_OPTS=-Duser.language=en -Duser.country=US

echo Cleaning project...
flutter clean

echo Installing dependencies...
flutter pub get

echo Generating code...
flutter packages pub run build_runner build --delete-conflicting-outputs

echo Building and running app...
flutter run --verbose

pause
