# Set encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# Change to project directory
Set-Location "d:\invoice"

Write-Host "=== Invoice App Setup and Run ===" -ForegroundColor Cyan
Write-Host ""

Write-Host "1. Checking Flutter installation..." -ForegroundColor Green
try {
    flutter doctor -v
    Write-Host "✓ Flutter check completed" -ForegroundColor Green
} catch {
    Write-Host "✗ Flutter not found. Please install Flutter first." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "`n2. Cleaning previous builds..." -ForegroundColor Green
try {
    flutter clean
    Write-Host "✓ Clean completed" -ForegroundColor Green
} catch {
    Write-Host "✗ Clean failed" -ForegroundColor Yellow
}

Write-Host "`n3. Installing dependencies..." -ForegroundColor Green
try {
    flutter pub get
    Write-Host "✓ Dependencies installed" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to install dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "`n4. Generating code..." -ForegroundColor Green
try {
    flutter packages pub run build_runner build --delete-conflicting-outputs
    Write-Host "✓ Code generation completed" -ForegroundColor Green
} catch {
    Write-Host "✗ Code generation failed" -ForegroundColor Yellow
    Write-Host "Continuing anyway..." -ForegroundColor Yellow
}

Write-Host "`n5. Checking connected devices..." -ForegroundColor Green
try {
    flutter devices
    Write-Host "✓ Device check completed" -ForegroundColor Green
} catch {
    Write-Host "✗ Device check failed" -ForegroundColor Yellow
}

Write-Host "`n6. Running the app..." -ForegroundColor Green
Write-Host "Make sure you have a device connected or emulator running!" -ForegroundColor Yellow
Write-Host ""

try {
    flutter run --verbose
} catch {
    Write-Host "✗ Failed to run the app" -ForegroundColor Red
    Write-Host "Please check the error messages above" -ForegroundColor Yellow
}

Write-Host "`nApp execution finished." -ForegroundColor Cyan
Read-Host "Press Enter to exit"
