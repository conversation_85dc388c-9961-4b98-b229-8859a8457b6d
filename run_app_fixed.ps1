# Fix encoding and run Invoice App
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# Set English locale
$env:LANG = "en_US.UTF-8"
$env:LC_ALL = "en_US.UTF-8"
$env:GRADLE_OPTS = "-Duser.language=en -Duser.country=US"

Set-Location "d:\invoice"

Write-Host "=== Invoice App - Fixed Encoding Run ===" -ForegroundColor Cyan
Write-Host ""

Write-Host "1. Setting English locale..." -ForegroundColor Green
Write-Host "   LANG: $env:LANG" -ForegroundColor Gray
Write-Host "   LC_ALL: $env:LC_ALL" -ForegroundColor Gray
Write-Host "   GRADLE_OPTS: $env:GRADLE_OPTS" -ForegroundColor Gray

Write-Host "`n2. Cleaning project..." -ForegroundColor Green
flutter clean

Write-Host "`n3. Installing dependencies..." -ForegroundColor Green
flutter pub get

Write-Host "`n4. Checking devices..." -ForegroundColor Green
flutter devices

Write-Host "`n5. Running app..." -ForegroundColor Green
Write-Host "Make sure your device is connected!" -ForegroundColor Yellow
Write-Host ""

flutter run --verbose

Write-Host "`nApp execution finished." -ForegroundColor Cyan
Read-Host "Press Enter to exit"
