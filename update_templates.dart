import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

void main() async {
  print('🔄 Starting template update process...');
  
  try {
    // Get database path
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'invoice_database.db');
    
    print('📂 Database path: $path');
    
    // Check if database exists
    if (!await File(path).exists()) {
      print('❌ Database file not found!');
      return;
    }
    
    // Open database
    final db = await openDatabase(path);
    
    // Update all invoices to use Modern template
    final result = await db.rawUpdate('''
      UPDATE invoices 
      SET template = ? 
      WHERE template IS NULL OR template = '' OR template = 'Default'
    ''', ['Modern']);
    
    print('✅ Updated $result invoices to use Modern template');
    
    // Verify the update
    final invoices = await db.query('invoices', columns: ['id', 'invoice_number', 'template']);
    
    print('📋 Current invoices:');
    for (final invoice in invoices) {
      print('  - ${invoice['invoice_number']}: ${invoice['template']}');
    }
    
    await db.close();
    print('🎉 Template update completed successfully!');
    
  } catch (e) {
    print('❌ Error updating templates: $e');
  }
}
